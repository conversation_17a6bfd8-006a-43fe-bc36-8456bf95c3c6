#App 
APP_VERSION = 1.0.0
APP_PORT = 9000
APP_HOST = localhost
APP_BASE_URL = localhost:9000
APP_PROTOTYPE = http
APP_ENV=local

#API Versioning
VALID_API_VERSIONS = 1,2
DEFAULT_API_VERSION = 1

#Database
DB_URL = mongodb://localhost:27017/smash

#Auth0
AUTH_CHECK = https://roles/role
AUTH0_DOMAIN = mysmash.eu.auth0.com
AUTH0_CLIENT_ID = xxxxxxxxxxxxxxxxxxxxxxxxxxxx
AUTH0_SECRET = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AUTH0_REDIRECT_URI = http://localhost:3000/verify

#Auth0 m2m
M2M_CLIENT_ID = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
M2M_CLIENT_SECRET = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
M2M_API_URL = https://mysmash.eu.auth0.com/oauth/token

#CORS
ALLOWED_ORIGIN = http://localhost:3000,http://localhost:8080
SKIP_USER_VALIDATION_ON_ROUTES= /v1/auth/createUser,/v1/auth,/v1/company/{id},/v1/company/list

#DB record
INIT_RECORD = 10

#User identity manager
IDENTITY_MANGER_URL = http://localhost:8000
AGENDA_DB_URL = mongodb://localhost:27017/smash_agenda

#Image compress
IMG_QUALITY = 60
IMG_COMPRESS_LEVEL = 9

#AWS
AWS_ACCESS_KEY_ID = xxxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_SECRET_ACCESS_KEY = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION = eu-west-2
AWS_BUCKET_NAME = smash-local-dev-20200723112447493300000001

#Elastic Search
ELASTIC_SEARCH_URL = https://1f70e1cf5c3a4ee98ee7abae9e9d8926.eu-west-1.aws.found.io:9243
ELASTIC_API_ID = xxxxxxxxxxxxxxxxxxxxxxxx
ELASTIC_API_KEY = xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ELASTIC_SEARCH_INDEX = local_dev_smash

#ElasticAPM 
IS_APM=false
ELASTIC_APM_SERVICE_NAME=SMASH_API_ON_DEV
ELASTIC_APM_SECRET_TOKEN=
ELASTIC_APM_SERVER_URL=

#LOG
DEBUG_LOG = false
LOG_PAYLOAD = false
LOG_REQUEST_START = false
LOG_REQUEST_COMPLETE = false
IGNORE_URLS_LOG = /v1/health,

#Getty Image
GETTY_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GETTY_CLIENT_SECRET=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GETTY_AUTH_URL=https://api.gettyimages.com/oauth2/token
GETTY_AUTH_REDIRECT_URI=http://localhost:3000/project/getty/verify

#MANDRILL env
FROM_EMAIL_ADDRESS = <EMAIL>
MANDRILL_API_KEY= xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
WEBAPP_BASE_URL = http://localhost:3000/project/snap/

#Getty Google form
GETTY_SPREADSHEET_ID = xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GETTY_SPREADSHEET_RANG = Sheet1
PUBLIC_API_AUTH_KEY=pulicapiauthkey