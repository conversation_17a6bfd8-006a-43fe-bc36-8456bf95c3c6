#App
export APP_VERSION=1.0.0
export APP_PORT=9000
export APP_HOST=localhost
export APP_BASE_URL=localhost:9000
export APP_PROTOTYPE=http

#API Versioning
export VALID_API_VERSIONS=1,2
export DEFAULT_API_VERSION=1

#Database
export DB_URL=mongodb://localhost:27017/smash_test

#Auth0
export AUTH_CHECK=https://roles/role
export AUTH0_DOMAIN=mysmash.eu.auth0.com
export AUTH0_CLIENT_ID=some
export AUTH0_SECRET=some_secret
export AUTH0_REDIRECT_URI=http://localhost:3000/verify

#Auth0 m2m
export M2M_CLIENT_ID=m2m_client_dummy
export M2M_CLIENT_SECRET=m2m_client_secret
export M2M_API_URL=https://mysmash.eu.auth0.com/oauth/token

#CORS
export ALLOWED_ORIGIN=http://localhost:3000,http://localhost:8080
export SKIP_USER_VALIDATION_ON_ROUTES=/v1/auth/createUser,/v1/auth,/v1/company/{id},/v1/company/list

#DB record
export INIT_RECORD=10

#User identity manager
export IDENTITY_MANGER_URL=http://localhost:8000

#Image compress
export IMG_QUALITY=60
export IMG_COMPRESS_LEVEL=9

#AWS
export AWS_ACCESS_KEY_ID=XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
export AWS_SECRET_ACCESS_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
export AWS_REGION=eu-west-2
export AWS_BUCKET_NAME=smash-bucket

#Elastic Search
export ELASTIC_SEARCH_URL=some_elasticsearch_url
export ELASTIC_API_ID=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
export ELASTIC_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
export ELASTIC_SEARCH_INDEX=local-dev-activity

#ElasticAPM
export IS_APM=false
export ELASTIC_APM_SERVICE_NAME=SAMPLE_NAME
export ELASTIC_APM_SECRET_TOKEN=XXXXXXXXXXXXXXXXXXXXX
export ELASTIC_APM_SERVER_URL=https://XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

#LOG
export DEBUG_LOG=false
export LOG_PAYLOAD=false
export LOG_REQUEST_START=false
export LOG_REQUEST_COMPLETE=false
export IGNORE_URLS_LOG=/v1/health,

#MANDRILL
export FROM_EMAIL_ADDRESS=<EMAIL>
export WEBAPP_BASE_URL=http://localhost:3000

#Getty_Image
export GETTY_AUTH_URL=https://api.gettyimages.com/oauth2/token
export GETTY_AUTH_REDIRECT_URI=http://localhost:3000/project/getty/verify


#GOOGLE_SHEET
export GETTY_SPREADSHEET_CLIENT_EMAIL=<EMAIL>
export GETTY_SPREADSHEET_RANG=Sheet1

# Email env
export SALES_EMAIL_ADDRESS="<EMAIL>"
export NO_REPLAY_EMAIL="<EMAIL>"
export CUPID_EMAIL_ADDRESS="<EMAIL>"
export ADMIN_EMAIL_ADDRESS="<EMAIL>"
export ADMIN_EMAILS="<EMAIL>,<EMAIL>"

#CUSTOMER_IO
export EMAIL_TEMPLATE_DATA="{\"gettyInfoTemplateId\":\"2\",\"inviteTemplateId\":\"3\",\"welcomeTemplateId\":\"4\",\"salesEstimateTemplateId\":\"5\",\"deleteUserTemplateId\":\"10\",\"accountCreateLinkTemplateId\":\"14\",\"submissionFeedbackTemplateId\":\"15\",\"calloutPublishTemplateId\":\"16\",\"slateTemplateId\":\"17\",\"calloutReadyToReviewTemplateId\":\"18\",\"paymentSucceededTemplateId\":\"20\"}"


#For Test only don't use in production
export TOKEN_SECRET=testingtailwisesecret
export PUBLIC_API_AUTH_KEY=pulicapiauthkey

export STRIPE_WEBHOOK_SECRET=XXXXXXXXXXXXXXXXXXXXX
export STRIPE_SECRET_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
export STRIPE_PUBLIC_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
export STRIPE_PROMOTION_CODE=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
