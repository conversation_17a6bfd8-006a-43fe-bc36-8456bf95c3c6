const { get } = require('lodash');
const { errorHandler } = require('../../../utils/errorHandler');
const Stripe = require('../../../../config/stripe');

class StripeService {
  /**
   * Constructor
   */
  constructor() {
    this.stripe = Stripe.get();
  }

  log(message, error = null) {
    if (error) {
      global.logger().error(error, message);
    } else {
      global.logger().info(message);
    }
  }

  async checkCustomerExists(email) {
    const customers = await this.stripe.customers.list({
      email,
      limit: 1, // Limit to 1 to check if any customer exists with this email
    });

    if (customers.data.length > 0) {
      return customers.data[0]; // The customer object if found
    } else {
      return null; // Customer not found
    }
  }

  async createProduct(name, metadata) {
    return await this.stripe.products.create({
      name,
      active: true,
      metadata,
    });
    // return product;
  }

  async customerPaymentIntent(customerId) {
    return await this.stripe.paymentIntents.list({
      customer: customerId, // Filter by customer ID
      limit: 1, // Retrieve only the most recent payment intent
    });
  }

  async updatePaymentIntents(resp) {
    return await this.stripe.paymentIntents.update(resp);
  }

  async cancelPaymentIntent(paymentIntentId) {
    return await this.stripe.paymentIntents.cancel(paymentIntentId);
  }

  async confirmPaymentIntent(paymentIntentId) {
    return await this.stripe.paymentIntents.confirm(paymentIntentId);
  }

  async retrievePromotionCodes(couponCode) {
    return await this.stripe.promotionCodes.list(couponCode);
  }

  async retrieveCoupons(couponCode) {
    return await this.stripe.coupons.retrieve(couponCode);
  }

  async createPrice(productId, amount, recurring) {
    return await this.stripe.prices.create({
      product: productId,
      unit_amount: amount, // Amount in cents ($5.00)
      currency: 'usd',
      recurring,
    });
  }

  /**
   * To get the stripe billing portal url
   *
   * @param {string} customerId - customer id
   * @returns {Promise<Object>} - response object
   */
  async getStripeBillingPortalUrl(customerId) {
    try {
      this.log('StripePaymentsService.getStripeBillingPortalUrl() called!');
      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
      });
      return session.url;
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getStripeBillingPortalUrl');
      errorHandler(err);
    }
  }

  /**
   * To get the stripe checkout page url
   *
   * @param {string} customerId - customer id
   * @param {string} priceId - price plan id
   * @returns {Promise<Object>} - response object
   */
  async getStripeCheckoutLink(customerId, priceId, metadata) {
    try {
      // Create a new Checkout session for the subscription
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId, // The Stripe customer ID
        payment_method_types: ['card'], // Payment methods to accept
        mode: 'subscription', // Mode for subscription
        line_items: [
          {
            price: priceId, // The price ID for the subscription plan
            quantity: 1, // Subscription quantity
          },
        ],
        success_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
        cancel_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
        subscription_data: { metadata },
        allow_promotion_codes: true,
      });

      // Return the session URL for redirecting the customer
      return session.url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Could not create checkout session');
    }
  }

  /**
   * To get the payment intent
   *
   * @param {string} paymentIntentId - payment intent id
   * @returns {Promise<Object>} - response object
   */
  async getPaymentIntent(paymentIntentId) {
    try {
      this.log('StripePaymentsService.getPaymentIntent() called!');
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getPaymentIntent');
      errorHandler(err);
    }
  }

  /**
   * To get the payment intent
   *
   * @param {string} paymentIntentId - payment intent id
   * @returns {Promise<Object>} - response object
   */
  async getCustomerSubscription(customer) {
    try {
      this.log('StripePaymentsService.getPaymentIntent() called!');
      return await this.stripe.subscriptions.list(customer);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getPaymentIntent');
      errorHandler(err);
    }
  }

  /**
   * To get the payment intent
   *
   * @param {string} paymentIntentId - payment intent id
   * @returns {Promise<Object>} - response object
   */
  async createPaymentIntent(paymentIntentPayload) {
    try {
      this.log('StripePaymentsService.createPaymentIntent called!');
      return await this.stripe.paymentIntents.create(paymentIntentPayload);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.createPaymentIntent');
      errorHandler(err);
    }
  }

  /**
   * To search the customer
   *
   * @param {string} email - customer email
   * @returns {Promise<Object>} - response object
   */
  async searchCustomer(email) {
    try {
      this.log('StripePaymentsService.searchCustomer() called!');
      const result = await this.stripe.customers.search({
        query: `email:'${email}'`,
      });
      if (get(result, 'data', []).length > 0) {
        return result.data[0];
      }
      return false;
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.searchCustomer');
      errorHandler(err);
    }
  }

  /**
   * To create a new customer
   *
   * @param {string} user - user object
   * @returns {Promise<Object>} - response object
   */
  async createCustomer(user) {
    try {
      this.log('StripePaymentsService.createCustomer() called!');
      return await this.stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: String(user.id),
          environment: process.env.APP_ENV,
        },
      });
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.createCustomer');
      errorHandler(err);
    }
  }

  /**
   * To create a new subscription
   *
   * @param {object} data - subscription object
   * @returns {Promise<Object>} - response object
   */
  async createSubscription(data) {
    try {
      this.log('StripePaymentsService.createSubscription() called!');
      return await this.stripe.subscriptions.create(data);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.createSubscription');
      errorHandler(err);
    }
  }

  /**
   * To update the subscription
   *
   * @param {string} subscriptionId - subscription id
   * @param {string} data - subscription object
   * @returns {Promise<Object>} - response object
   */
  async updateSubscription(subscriptionId, data) {
    try {
      this.log('StripePaymentsService.updateSubscription() called!');
      return await this.stripe.subscriptions.update(subscriptionId, data);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.updateSubscription');
      errorHandler(err);
    }
  }

  /**
   * To get the subscription
   *
   * @param {string} subscriptionId  - subscription id
   * @returns {Promise<Object>} - response object
   */
  async getSubscription(subscriptionId) {
    try {
      this.log('StripePaymentsService.getSubscription() called!');
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getSubscription');
      errorHandler(err);
    }
  }

  /**
   * To get the invoice
   */
  async getInvoice(invoiceId) {
    try {
      this.log('StripePaymentsService.getInvoice() called!');
      return await this.stripe.invoices.retrieve(invoiceId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getInvoice');
      errorHandler(err);
    }
  }

  async cancelSubsctiption(subscriptionId) {
    try {
      this.log('StripePaymentsService.cancelSubsctiption() called!');
      return await this.stripe.subscriptions.cancel(subscriptionId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.cancelSubsctiption');
      errorHandler(err);
    }
  }

  async upcomingInvoice(subscriptionId) {
    try {
      return await this.stripe.invoices.retrieveUpcoming({
        subscription: subscriptionId,
      });
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.upcomingInvoice');
      errorHandler(err);
    }
  }

  /**
   * To handle the handleInvoiceDraftDetele
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleInvoiceDraftDetele(eventData) {
    this.log('StripePaymentsService.handleInvoiceDraftDetele() called!');

    if (eventData.status === 'draft' && eventData.subscription) {
      const subscription = await this.stripe.subscriptions.retrieve(
        eventData.subscription,
      );

      const currentTime = Math.floor(Date.now() / 1000);
      if (subscription.trial_end && currentTime > subscription.trial_end) {
        await this.stripe.invoices.del(eventData.id);
      }
    }

    return eventData;
  }
}

module.exports = new StripeService();
