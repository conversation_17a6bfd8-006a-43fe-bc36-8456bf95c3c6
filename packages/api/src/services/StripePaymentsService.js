const { get, isEmpty, has, last } = require('lodash');
const moment = require('moment');
const BaseService = require('./BaseService');
const MailService = require('./MailService');
const { errorHandler } = require('../utils/errorHandler');
const Stripe = require('../../config/stripe');
const AuthService = require('./AuthService');
const UserService = require('./UserService');

const userService = new UserService();
const { getCreatorFormateObject } = require('../utils/helpers');

class StripePaymentsService extends BaseService {
  /**
   * Constructor
   */
  constructor() {
    super();
    this.stripe = Stripe.get();
  }

  /**
   * This function is responsible to update local subscription DB
   *
   * @param {string} id - subscription id
   * @param {object} data - subscription data
   * @param {object} options - options object
   * @returns {Promise<Object>} - response object
   */
  async createSubscriptionInDb(data) {
    this.log('StripePaymentsService.updateLocalSubscription() called!');
    return await this.save('Subscriptions', data);
  }

  /**
   * This function is responsible to update local subscription DB
   *
   * @param {string} id - subscription id
   * @param {object} data - subscription data
   * @param {object} options - options object
   * @returns {Promise<Object>} - response object
   */
  async updateLocalSubscription(id, data, options = {}) {
    this.log('StripePaymentsService.updateLocalSubscription() called!');
    return await this.update('Subscriptions', { _id: id }, data, options);
  }

  async checkCustomerExists(email) {
    const customers = await this.stripe.customers.list({
      email,
      limit: 1, // Limit to 1 to check if any customer exists with this email
    });

    if (customers.data.length > 0) {
      return customers.data[0]; // The customer object if found
    } else {
      return null; // Customer not found
    }
  }

  async createProduct(name, metadata) {
    return await this.stripe.products.create({
      name,
      active: true,
      metadata,
    });
    // return product;
  }

  async createPrice(productId, amount, recurring) {
    return await this.stripe.prices.create({
      product: productId,
      unit_amount: amount, // Amount in cents ($5.00)
      currency: 'gbp',
      recurring,
    });
  }

  /**
   * This function is responsible to update local subscription DB using any field
   *
   * @param {string} id - subscription id
   * @param {object} data - subscription data
   * @param {object} options - options object
   * @returns {Promise<Object>} - response object
   */
  async updateLocalDbSubscription(where, data, options = {}) {
    this.log('StripePaymentsService.updateLocalDbSubscription() called!');
    return await this.update('Subscriptions', where, data, options);
  }

  /**
   * Get subscription
   *
   * @param {Object} query - query object
   * @param {Object} options - options object
   * @returns {Promise<Object>} - response object
   */
  async fetchSubscription(query, options = {}) {
    this.log('StripePaymentsService.fetchSubscription() called!');
    return await this.findOne('Subscriptions', query, options);
  }

  /**
   * Get subscription
   *
   * @param {Object} query - query object
   * @param {Object} options - options object
   * @returns {Promise<Object>} - response object
   */
  async fetchAllSubscription(query, options = {}) {
    this.log('StripePaymentsService.fetchAllSubscription() called!');
    return await this.find('Subscriptions', query, options);
  }

  /**
   * To get the active subscriptions
   *
   * @param {String} userId - user id
   * @returns {Promise<Object>} - response object
   */
  async getActiveSubscription(userId) {
    try {
      this.log('StripePaymentsService.getActiveSubscription() called!');
      const now = Math.floor(Date.now() / 1000);
      return await this.findOne('Subscriptions', {
        'user.userId': userId,
        currentPeriodStart: { $lte: now },
        currentPeriodEnd: { $gte: now },
        status: { $in: ['active', 'trialing'] },
        paymentGateway: 'stripe',
      });
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getSubscriptions');
      errorHandler(err);
    }
  }

  /**
   * To get the stripe billing portal url
   *
   * @param {string} customerId - customer id
   * @returns {Promise<Object>} - response object
   */
  async getStripeBillingPortalUrl(customerId) {
    try {
      this.log('StripePaymentsService.getStripeBillingPortalUrl() called!');
      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
      });
      return session.url;
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getStripeBillingPortalUrl');
      errorHandler(err);
    }
  }

  /**
   * To get the stripe checkout page url
   *
   * @param {string} customerId - customer id
   * @param {string} priceId - price plan id
   * @returns {Promise<Object>} - response object
   */
  async getStripeCheckoutLink(customerId, priceId, metadata) {
    try {
      // Create a new Checkout session for the subscription
      const session = await this.stripe.checkout.sessions.create({
        customer: customerId, // The Stripe customer ID
        payment_method_types: ['card'], // Payment methods to accept
        mode: 'subscription', // Mode for subscription
        line_items: [
          {
            price: priceId, // The price ID for the subscription plan
            quantity: 1, // Subscription quantity
          },
        ],
        success_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
        cancel_url: `${process.env.WEBAPP_BASE_URL}/myaccount`,
        subscription_data: { metadata },
      });

      // Return the session URL for redirecting the customer
      return session.url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Could not create checkout session');
    }
  }

  /**
   * To get the payment intent
   *
   * @param {string} paymentIntentId - payment intent id
   * @returns {Promise<Object>} - response object
   */
  async getPaymentIntent(paymentIntentId) {
    try {
      this.log('StripePaymentsService.getPaymentIntent() called!');
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getPaymentIntent');
      errorHandler(err);
    }
  }

  /**
   * To search the customer
   *
   * @param {string} email - customer email
   * @returns {Promise<Object>} - response object
   */
  async searchCustomer(email) {
    try {
      this.log('StripePaymentsService.searchCustomer() called!');
      const result = await this.stripe.customers.search({
        query: `email:'${email}'`,
      });
      if (get(result, 'data', []).length > 0) {
        return result.data[0];
      }
      return false;
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.searchCustomer');
      errorHandler(err);
    }
  }

  /**
   * To create a new customer
   *
   * @param {string} user - user object
   * @returns {Promise<Object>} - response object
   */
  async createCustomer(user) {
    try {
      this.log('StripePaymentsService.createCustomer() called!');
      return await this.stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: String(user.id),
          environment: process.env.APP_ENV,
        },
      });
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.createCustomer');
      errorHandler(err);
    }
  }

  /**
   * To create a new subscription
   *
   * @param {object} data - subscription object
   * @returns {Promise<Object>} - response object
   */
  async createSubscription(data) {
    try {
      this.log('StripePaymentsService.createSubscription() called!');
      return await this.stripe.subscriptions.create(data);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.createSubscription');
      errorHandler(err);
    }
  }

  /**
   * To update the subscription
   *
   * @param {string} subscriptionId - subscription id
   * @param {string} data - subscription object
   * @returns {Promise<Object>} - response object
   */
  async updateSubscription(subscriptionId, data) {
    try {
      this.log('StripePaymentsService.updateSubscription() called!');
      return await this.stripe.subscriptions.update(subscriptionId, data);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.updateSubscription');
      errorHandler(err);
    }
  }

  /**
   * To get the subscription
   *
   * @param {string} subscriptionId  - subscription id
   * @returns {Promise<Object>} - response object
   */
  async getSubscription(subscriptionId) {
    try {
      this.log('StripePaymentsService.getSubscription() called!');
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getSubscription');
      errorHandler(err);
    }
  }

  /**
   * To get the invoice
   */
  async getInvoice(invoiceId) {
    try {
      this.log('StripePaymentsService.getInvoice() called!');
      return await this.stripe.invoices.retrieve(invoiceId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.getInvoice');
      errorHandler(err);
    }
  }

  /**
   * To handle the strips subscription
   *
   * @param {Object} payload - request object
   * @param {Object} user - user object
   * @returns {Promise<Object>} - response object
   */
  async handleSubscription(preSubscription, payload, user) {
    this.log('StripePaymentsService.handleSubscription() called!');
    const planId = payload.planId;

    try {
      /* Fetch customer id from existing subscription in db */
      const customerId = get(preSubscription, 'customerId', false);

      /* Create a new customer if the user doesn't have any subscriptions */
      let customer;
      if (customerId) {
        customer = { id: customerId };
      } else {
        customer = await this.createCustomer(user);
      }

      // Handle existing subscriptions - preserve trial periods for upgrades
      let endTrial = 0;
      let isTrialUpgrade = false;

      if (
        preSubscription &&
        has(preSubscription, 'subscriptionId') &&
        get(preSubscription, 'type', null) === 'trial'
      ) {
        // This is a trial upgrade - preserve the trial end date
        endTrial = preSubscription.response.current_period_end;
        isTrialUpgrade = true;

        this.log(
          `StripePaymentsService.createSubscription: Trial upgrade detected. Trial ends at: ${endTrial}`,
        );

        // Cancel the existing trial subscription to prevent double billing
        const stripeSubscriptionId = get(preSubscription, 'subscriptionId');
        await this.cancelSubsctiption(stripeSubscriptionId);
      } else if (
        preSubscription &&
        has(preSubscription, 'subscriptionId') &&
        get(preSubscription, 'type', null) !== 'free'
      ) {
        // Cancel other non-free subscriptions
        const stripeSubscriptionId = get(preSubscription, 'subscriptionId');
        await this.cancelSubsctiption(stripeSubscriptionId);
      }

      const currentLocalSubscriptionData = await this.save('Subscriptions', {
        user: getCreatorFormateObject(user),
        customerId: customer.id,
        planId,
      });

      const subscriptionOptions = {
        customer: customer.id,
        items: [{ price: payload.priceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: {
          save_default_payment_method: 'on_subscription',
          payment_method_types: ['card'],
        },
        expand: ['latest_invoice.payment_intent'],
        description: `Subscription initiated by user: ${user.email}`,
        metadata: {
          userId: String(user._id),
          type: payload.type,
          userEmail: user.email,
          planId,
          localSubscriptionId: String(currentLocalSubscriptionData._id),
          remainingDays: endTrial,
          environment: process.env.APP_ENV,
          isTrialUpgrade: isTrialUpgrade ? 'true' : 'false',
        },
      };

      // If this is a trial upgrade, set the trial_end to preserve the original trial period
      if (isTrialUpgrade && endTrial > 0) {
        subscriptionOptions.trial_end = endTrial;
        subscriptionOptions.proration_behavior = 'none'; // Prevent proration during trial

        this.log(
          `StripePaymentsService.createSubscription: Setting trial_end to ${endTrial} for trial upgrade`,
        );
      }

      if (payload.type === 'trial') {
        subscriptionOptions.trial_period_days = 7;
      }
      // const subscription = await this.createSubscription(subscriptionOptions);

      // await this.update(
      //   'Subscriptions',
      //   { _id: currentLocalSubscriptionData._id },
      //   {
      //     subscriptionId: subscription.id,
      //     paymentGateway: 'stripe',
      //     amount: subscription.total,
      //     startDate: moment(subscription.current_period_start * 1000),
      //     endDate: moment(subscription.current_period_end * 1000),
      //     renewalDate: get(subscription, 'current_period_end'),
      //     cancelAtPeriodEnd: get(subscription, 'cancel_at_period_end'),
      //     response: subscription,
      //     status: 'draft',
      //     type: payload.type,
      //   },
      // );
      // return getPaymentInfo(subscription);
      return {};
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.handleSubscription');
      errorHandler(err);
    }
  }

  /**
   * To handle the payment succeeded event
   *
   * @param {object} eventData - event data
   * @param {object} subscription - subscription object
   * @param {string} transactionId - transaction id
   * @returns {Promise<void>} - response object
   */
  async handlePaymentSucceeded(eventData, subscription) {
    /* Mark Subscription paid in DB */
    const metadata = get(eventData, 'subscription_details.metadata', {});

    // Check if this is a trial upgrade and cancel the original trial subscription
    const isTrialUpgrade = get(metadata, 'isTrialUpgrade', 'false') === 'true';
    const originalTrialSubscriptionId = get(
      metadata,
      'originalTrialSubscriptionId',
      null,
    );

    if (isTrialUpgrade && originalTrialSubscriptionId) {
      this.log(
        `StripePaymentsService.handlePaymentSucceeded: Canceling original trial subscription ${originalTrialSubscriptionId} after payment completion`,
      );
      try {
        await this.cancelSubsctiption(originalTrialSubscriptionId);
        this.log(
          `StripePaymentsService.handlePaymentSucceeded: Successfully canceled original trial subscription ${originalTrialSubscriptionId}`,
        );
      } catch (error) {
        this.log(
          `StripePaymentsService.handlePaymentSucceeded: Error canceling original trial subscription ${originalTrialSubscriptionId}: ${error.message}`,
        );
      }
    }

    const subscriptionList = await this.stripe.subscriptions.list({
      customer: eventData.customer,
    });
    for (const subscriptionData of subscriptionList.data) {
      if (
        subscriptionData.id !== subscription.id &&
        subscriptionData.id !== originalTrialSubscriptionId
      ) {
        await this.cancelSubsctiption(subscriptionData.id);
      }
    }
    const result = await this.update(
      'Subscriptions',
      { subscriptionId: subscription.id },
      {
        type: metadata.type,
        startDate: moment(subscription.current_period_start * 1000),
        response: eventData,
        status: 'active',
      },
    );
    // Check if subscription exists
    if (!result) {
      this.log(`Subscription: ${subscription.id} dose not exist!`);
      return false;
    }
    const accessToken = await AuthService.authM2MToken();
    const userMeta = {
      cancelAtPeriodEnd: false,
      amount: eventData.amount_paid,
      expired: subscription.current_period_end,
      planId: metadata.planId,
      customerId: eventData.customer,
      subscriptionId: eventData.subscription,
      type: metadata.type,
      status: 'active',
    };
    await userService.updateUserMetaIM(
      get(metadata, 'userEmail'),
      userMeta,
      accessToken,
    );

    /* Send payment email to admin */
    await MailService.paymentSucceededEmailToAdmin({
      agenda_type: 'payment_succeeded',
      message_data: {
        customerEmail: get(result, 'user.email'),
        invoice: get(result, 'currentInvoice.invoice_pdf'),
      },
      subject: `Payment Succeeded | ${get(result, 'user.email')}`,
    });

    this.log(`Subscription:${subscription.id} settled successfully.`);
  }

  /**
   * To handle the customer subscription deleted webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleCustomerSubscriptionDeletedEvent(eventData) {
    this.log(
      'StripePaymentsService.handleCustomerSubscriptionDeletedEvent() called!',
    );
    const metadata = get(eventData, 'metadata', {});
    const subscriptionDb = await this.fetchAllSubscription({
      'user.email': metadata.userEmail,
    });
    const previousSubscription = last(subscriptionDb);
    if (previousSubscription.status === 'draft') {
      return eventData;
    }
    const accessToken = await AuthService.authM2MToken();
    const userMeta = {
      planId: metadata.planId,
      customerId: eventData.customer,
      subscriptionId: eventData.subscription,
      type: metadata.type,
      cancelAtPeriodEnd: false,
      status: 'expired',
    };
    await userService.updateUserMetaIM(
      metadata.userEmail,
      userMeta,
      accessToken,
    );
    /* Update subscription status in DB */
    await this.update(
      'Subscriptions',
      { subscriptionId: eventData.id },
      { status: 'expired', type: metadata.type, response: eventData },
    );

    return eventData;
  }

  /**
   * To handle the customer subscription updated webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleCustomerSubscriptionUpdatedEvent(eventData) {
    this.log(
      'StripePaymentsService.handleCustomerSubscriptionUpdatedEvent() called!',
    );
    const amount = get(eventData, 'items.data[0].price.unit_amount');
    const expired = moment(eventData.current_period_end * 1000);
    const metadata = get(eventData, 'metadata', {});

    if (!isEmpty(metadata)) {
      const accessToken = await AuthService.authM2MToken();
      const userMeta = {
        amount,
        expired,
        planId: metadata.planId,
        customerId: eventData.customer,
        subscriptionId: eventData.subscription,
        type: metadata.type,
        status: 'active',
      };
      if (
        eventData.status === 'past_due' ||
        eventData.status === 'incomplete_expired'
      ) {
        userMeta.status = 'expired';
      }
      if (eventData.cancel_at_period_end) {
        userMeta.cancelAtPeriodEnd = true;
      } else {
        userMeta.cancelAtPeriodEnd = false;
      }
      await userService.updateUserMetaIM(
        metadata.userEmail,
        userMeta,
        accessToken,
      );
      const subscriptionData = {
        type: metadata.type,
        startDate: moment(eventData.current_period_start * 1000),
        response: eventData,
        status:
          eventData.status === 'past_due' ||
            eventData.status === 'incomplete_expired'
            ? 'expired'
            : 'active',
      };
      if (eventData.cancel_at_period_end) {
        subscriptionData.cancelAtPeriodEnd = true;
      } else {
        subscriptionData.cancelAtPeriodEnd = false;
      }
      await this.update(
        'Subscriptions',
        { subscriptionId: eventData.id },
        subscriptionData,
      );
    }
    return eventData;
  }

  /**
   * To handle the invoice payment_failed webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleInvoicePaymentFailedEvent(eventData) {
    this.log('StripePaymentsService.handleInvoicePaymentFailedEvent() called!');
    const subscriptionId = eventData.subscription;
    const metadata = get(eventData, 'subscription_details.metadata', {});
    try {
      const subscription = await this.getSubscription(subscriptionId);
      if (subscription.status === 'canceled') {
        const subscriptionDb = await this.fetchAllSubscription({
          'user.email': metadata.userEmail,
        });
        const previousSubscription = last(subscriptionDb);

        if (previousSubscription.status === 'draft') {
          return eventData;
        }
        const accessToken = await AuthService.authM2MToken();
        const userMeta = {
          planId: metadata.planId,
          customerId: eventData.customer,
          subscriptionId: eventData.subscription,
          status: 'expired',
        };
        await userService.updateUserMetaIM(
          metadata.userEmail,
          userMeta,
          accessToken,
        );
      }
      /* Mark Subscription paid in DB */
      await this.update(
        'Subscriptions',
        { subscriptionId },
        {
          currentInvoice: eventData,
          status:
            subscription.status === 'canceled'
              ? 'expired'
              : subscription.status,
        },
      );

      return eventData;
    } catch (err) {
      this.log(err, 'Error in invoice.payment_failed.payment_failed');
      errorHandler(err);
    }
  }

  async cancelSubsctiption(subscriptionId) {
    try {
      this.log('StripePaymentsService.cancelSubsctiption() called!');
      return await this.stripe.subscriptions.cancel(subscriptionId);
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.cancelSubsctiption');
      errorHandler(err);
    }
  }

  async upcomingInvoice(subscriptionId) {
    try {
      return await this.stripe.invoices.retrieveUpcoming({
        subscription: subscriptionId,
      });
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.upcomingInvoice');
      errorHandler(err);
    }
  }

  /**
   * To handle the invoice payment_succeeded webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleInvoicePaymentSucceededEvent(eventData) {
    this.log(
      'StripePaymentsService.handleInvoicePaymentSucceededEvent() called!',
    );
    const subscriptionId = eventData.subscription;
    const metadata = get(eventData, 'subscription_details.metadata', {});

    // Check if this is a trial upgrade - if so, we should not process payment during trial
    const isTrialUpgrade = get(metadata, 'isTrialUpgrade', 'false') === 'true';
    const subscription = await this.getSubscription(subscriptionId);

    if (isTrialUpgrade && subscription.status === 'trialing') {
      this.log(
        `StripePaymentsService.handleInvoicePaymentSucceededEvent: Skipping payment processing for trial upgrade subscription ${subscriptionId} during trial period`,
      );
      return eventData; // Skip processing during trial for upgrades
    }

    // Check if this is a payment intent success for trial upgrade (no subscription exists yet)
    if (!subscriptionId && eventData.payment_intent) {
      const paymentIntent = await this.getPaymentIntent(
        eventData.payment_intent,
      );
      const paymentIntentMetadata = get(paymentIntent, 'metadata', {});
      const isTrialUpgrade =
        get(paymentIntentMetadata, 'isTrialUpgrade', 'false') === 'true';

      if (isTrialUpgrade) {
        this.log(
          `StripePaymentsService.handleInvoicePaymentSucceededEvent: Handling payment intent success for trial upgrade`,
        );
        return await this.handleTrialUpgradePaymentSuccess(
          eventData,
          paymentIntent,
        );
      }
    }

    switch (eventData.billing_reason) {
      case 'subscription_create': {
        try {
          /* Retrieve the payment intent and update subscription */
          const paymentIntentId = eventData.payment_intent;

          if (paymentIntentId) {
            const intent = await this.getPaymentIntent(paymentIntentId);

            const subscription = await this.updateSubscription(subscriptionId, {
              default_payment_method: intent.payment_method,
              description: `Subscription created by user: ${metadata.userEmail}`,
            });
            /* Mark Subscription paid in DB */
            const remainingDays = get(metadata, 'remainingDays', 0);
            const currentPeriodEnd = get(
              subscription,
              'current_period_end',
              null,
            );

            const daysDifference = moment(remainingDays * 1000).diff(
              moment(),
              'days',
            );
            const updatedTimestamp = moment
              .unix(currentPeriodEnd)
              .add(daysDifference, 'days')
              .unix();

            await this.handlePaymentSucceeded(eventData, subscription);
            if (daysDifference > 0) {
              const accessToken = await AuthService.authM2MToken();
              const userMeta = {
                amount: eventData.amount_paid,
                expired: updatedTimestamp,
              };
              await userService.updateUserMetaIM(
                get(metadata, 'userEmail'),
                userMeta,
                accessToken,
              );
              await this.update(
                'Subscriptions',
                { subscriptionId: subscription.id },
                {
                  amount: eventData.amount_paid,
                  endDate: moment(updatedTimestamp * 1000),
                },
              );
              const updatedSubscriptionOptions = {
                trial_end: remainingDays,
                proration_behavior: 'none',
              };
              await this.updateSubscription(
                subscription.id,
                updatedSubscriptionOptions,
              );
            }
          }
        } catch (err) {
          this.log(
            err,
            'Error in invoice.payment_succeeded.subscription_create',
          );
          errorHandler(err);
        }
        break;
      }
      case 'subscription_update': {
        try {
          /* Retrieve the subscription  */
          const subscription = await this.updateSubscription(subscriptionId, {
            description: `Subscription updated by user: ${metadata.userEmail}`,
          });

          /* Mark Subscription paid in DB */
          await this.handlePaymentSucceeded(eventData, subscription);
        } catch (err) {
          this.log(
            err,
            'Error in invoice.payment_succeeded.subscription_create',
          );
          errorHandler(err);
        }
        break;
      }
      case 'subscription_cycle': {
        try {
          /* Retrieve the subscription  */
          const subscription = await this.updateSubscription(subscriptionId, {
            description: `Subscription updated by user: ${metadata.userEmail}`,
          });

          /* Mark Subscription paid in DB */
          await this.handlePaymentSucceeded(eventData, subscription);
        } catch (err) {
          this.log(
            err,
            'Error in invoice.payment_succeeded.subscription_create',
          );
          errorHandler(err);
        }
        break;
      }
      default: {
        this.log(`Unhandled billing reason: ${eventData.billing_reason}`);
      }
    }
    return eventData;
  }

  /**
   * To handle the checkout session event
   *
   * @param {object} eventData - event data
   * @returns {Promise<void>} - response object
   */
  async handleCheckoutSession(eventData) {
    const subscription = eventData.subscription;
    const subscriptionData = await this.getSubscription(subscription);
    // Access the metadata
    const metadata = subscriptionData.metadata;
    const subscriptionDb = await this.fetchAllSubscription({
      'user.email': metadata.userEmail,
    });
    const previousSubscription = last(subscriptionDb);
    if (
      has(previousSubscription, 'subscriptionId') &&
      get(previousSubscription, 'status', '') !== 'expired'
    ) {
      await this.cancelSubsctiption(
        get(previousSubscription, 'subscriptionId'),
      );
    }

    const customerId = eventData.customer;
    const autoRenew = !eventData.cancel_at_period_end;
    const renewalDate = eventData.current_period_end;
    const cancelAtPeriodEnd = eventData.cancel_at_period_end || false;

    /* Mark Subscription paid in DB */

    const accessToken = await AuthService.authM2MToken();
    const userMeta = {
      cancelAtPeriodEnd,
      customerId,
      amount: eventData.amount_total,
      expired: moment(subscription.current_period_end * 1000),
      planId: metadata.planId,
      subscriptionId: eventData.subscription,
      type: metadata.type,
      status: 'active',
    };
    const userData = await userService.updateUserMetaIM(
      metadata.userEmail,
      userMeta,
      accessToken,
    );
    try {
      await this.save('Subscriptions', {
        user: userData
          ? getCreatorFormateObject(userData.user)
          : { email: metadata.userEmail },
        planId: metadata.planId,
        type: metadata.type,
        startDate: subscriptionData.current_period_start,
        endDate: subscription.current_period_end,
        response: eventData,
        cancelAtPeriodEnd,
        renewalDate,
        autoRenew,
        subscriptionId: eventData.subscription,
        status: 'active',
      });
    } catch (error) {
      global.logger().error(error, `Error in BaseService.save`);
    }
    this.log(`handleCheckoutSession:${subscription.id} settled successfully.`);
    return eventData;
  }

  /**
   * To handle the customer subscription updated webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleSubscriptionCreateTrial(eventData) {
    this.log('StripePaymentsService.handleSubscriptionCreateTrial() called!');
    console.log('\n\n\n\n ==============================');
    console.log(eventData);
    const metadata = get(eventData, 'metadata', {});
    const accessToken = await AuthService.authM2MToken();
    if (eventData.status === 'trialing') {
      const userMeta = {
        expired: eventData.trial_end,
        planId: metadata.planId,
        amount: 'NA',
        customerId: eventData.customer,
        subscriptionId: eventData.subscription,
        type: metadata.type,
        status: 'active',
      };
      await userService.updateUserMetaIM(
        metadata.userEmail,
        userMeta,
        accessToken,
      );
      await this.update(
        'Subscriptions',
        { subscriptionId: eventData.id },
        {
          type: metadata.type,
          renewalDate: eventData.trial_end,
          status: 'active',
        },
      );
    }

    return eventData;
  }

  /**
   * Handle payment success for trial upgrade
   * Creates the actual subscription when payment is completed for trial upgrade
   *
   * @param {Object} eventData - invoice event data
   * @param {Object} paymentIntent - payment intent object
   * @returns {Promise<Object>} - response object
   */
  async handleTrialUpgradePaymentSuccess(eventData, paymentIntent) {
    this.log(
      'StripePaymentsService.handleTrialUpgradePaymentSuccess() called!',
    );

    const metadata = get(paymentIntent, 'metadata', {});
    const originalTrialSubscriptionId = get(
      metadata,
      'originalTrialSubscriptionId',
      null,
    );
    const trialEndTimestamp = get(metadata, 'trialEndTimestamp', null);
    const planId = get(metadata, 'planId', null);
    const priceId = get(metadata, 'priceId', null);
    const userEmail = get(metadata, 'userEmail', null);

    try {
      // Now create the actual subscription with the coupon discount applied
      const subscriptionData = {
        customer: paymentIntent.customer,
        items: [{ price: priceId }],
        default_payment_method: paymentIntent.payment_method,
        trial_end: trialEndTimestamp, // Preserve original trial period
        proration_behavior: 'none',
        metadata: {
          planId,
          userEmail,
          isTrialUpgrade: 'true',
          originalTrialSubscriptionId,
          trialUpgradeProcessed: 'true',
        },
      };

      // Apply the coupon if it was stored in metadata
      const couponId = get(metadata, 'couponId', null);
      if (couponId) {
        this.log(
          `StripePaymentsService.handleTrialUpgradePaymentSuccess: Applying coupon ${couponId} to subscription`,
        );
        subscriptionData.discounts = [{ promotion_code: couponId }];
      }

      const subscription = await this.createSubscription(subscriptionData);

      this.log(
        `StripePaymentsService.handleTrialUpgradePaymentSuccess: Created subscription ${subscription.id} for trial upgrade`,
      );

      // Cancel the original trial subscription now that payment is complete
      if (originalTrialSubscriptionId) {
        try {
          await this.cancelSubsctiption(originalTrialSubscriptionId);
          this.log(
            `StripePaymentsService.handleTrialUpgradePaymentSuccess: Canceled original trial subscription ${originalTrialSubscriptionId}`,
          );
        } catch (error) {
          this.log(
            `StripePaymentsService.handleTrialUpgradePaymentSuccess: Error canceling original trial subscription: ${error.message}`,
          );
        }
      }

      // Update user metadata to reflect the new subscription
      const accessToken = await AuthService.authM2MToken();
      const userMeta = {
        amount: eventData.amount_paid,
        expired: trialEndTimestamp,
        planId,
        customerId: paymentIntent.customer,
        subscriptionId: subscription.id,
        status: 'active',
      };

      await userService.updateUserMetaIM(userEmail, userMeta, accessToken);

      this.log(
        `StripePaymentsService.handleTrialUpgradePaymentSuccess: Trial upgrade completed successfully for ${userEmail}`,
      );

      return eventData;
    } catch (error) {
      this.log(
        `StripePaymentsService.handleTrialUpgradePaymentSuccess: Error processing trial upgrade: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * To handle the charge succeeded webhook event
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleChargeSucceeded(eventData) {
    this.log('StripePaymentsService.handleChargeSucceeded() called!');
    // For now, just log the event - can be extended later if needed
    this.log(`Charge succeeded: ${eventData.id} for amount: ${eventData.amount}`);
    return eventData;
  }

  /**
   * To handle the handleInvoiceDraftDetele
   *
   * @param {Object} eventData - event data
   * @returns {Promise<Object>} - response object
   */
  async handleInvoiceDraftDetele(eventData) {
    this.log('StripePaymentsService.handleInvoiceDraftDetele() called!');

    if (eventData.status === 'draft' && eventData.subscription) {
      const subscription = await this.stripe.subscriptions.retrieve(
        eventData.subscription,
      );

      const currentTime = Math.floor(Date.now() / 1000);
      if (subscription.trial_end && currentTime > subscription.trial_end) {
        await this.stripe.invoices.del(eventData.id);
      }
    }

    return eventData;
  }

  /**
   * Handle the stripe webhook
   *
   * @param {*} event - event object
   * @returns {Promise<Object>} - response object
   */
  async handleStripeWebhook(event) {
    try {
      this.log('StripePaymentsService.handleStripeWebhook() called!');
      this.log(event);
      console.log('============\n\n\n\n\n\n\n', event);
      /* Handle events */
      switch (event.type) {
        case 'customer.subscription.deleted': {
          const eventData = event.data.object;
          await this.handleCustomerSubscriptionDeletedEvent(eventData);
          break;
        }
        case 'customer.subscription.updated': {
          const eventData = event.data.object;
          await this.handleCustomerSubscriptionUpdatedEvent(eventData);
          break;
        }
        case 'invoice.payment_failed': {
          const eventData = event.data.object;
          // await this.handleInvoicePaymentFailedEvent(eventData);
          break;
        }
        case 'invoice.payment_succeeded': {
          const eventData = event.data.object;
          await this.handleInvoicePaymentSucceededEvent(eventData);
          break;
        }
        case 'checkout.session.completed': {
          const eventData = event.data.object;
          await this.handleCheckoutSession(eventData);
          break;
        }
        case 'invoice.created': {
          const eventData = event.data.object;
          // await this.handleInvoiceDraftDetele(eventData);
          break;
        }
        case 'customer.subscription.created': {
          const eventData = event.data.object;
          await this.handleSubscriptionCreateTrial(eventData);
          break;
        }
        case 'charge.succeeded': {
          const eventData = event.data.object;
          await this.handleChargeSucceeded(eventData);
          break;
        }
        default: {
          this.log(`Unhandled event type ${event.type}`);
        }
      }
    } catch (err) {
      this.log(err, 'Error in StripePaymentsService.handleStripeWebhook');
      errorHandler(err);
    }
  }
}

module.exports = new StripePaymentsService();
