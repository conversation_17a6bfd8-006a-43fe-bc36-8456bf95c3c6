const axios = require('axios');
const Boom = require('@hapi/boom');
const { get, isEmpty, find, includes } = require('lodash');
const jwtDecode = require('jwt-decode');
const Permission = require('../models/permission');

// Fix for Node.js v24+ compatibility - add missing util functions
/* eslint-disable n/no-deprecated-api */
const util = require('util');
if (!util.isFunction) {
  util.isFunction = function (arg) {
    return typeof arg === 'function';
  };
}
if (!util.isString) {
  util.isString = function (arg) {
    return typeof arg === 'string';
  };
}
if (!util.isNumber) {
  util.isNumber = function (arg) {
    return typeof arg === 'number';
  };
}
if (!util.isObject) {
  util.isObject = function (arg) {
    return typeof arg === 'object' && arg !== null;
  };
}
if (!util.isArray) {
  util.isArray = function (arg) {
    return Array.isArray(arg);
  };
}
/* eslint-enable n/no-deprecated-api */

class AuthService {
  /**
   * Get User data from the help of curl request on activate-im server
   * @param {*} jwtToken
   * @param {*} loggedUserEmail
   */
  static async getUserProfileData(jwtToken, loggedUserEmail) {
    try {
      global.logger().info('getUserProfileData method called!');
      const response = await axios({
        method: 'GET',
        url: `${process.env.IDENTITY_MANGER_URL}/v1/user/detail`,
        headers: {
          Authorization: `Bearer ${jwtToken}`,
        },
      });
      global
        .logger()
        .info(
          `Fetch user data from ${process.env.IDENTITY_MANGER_URL}/v1 server with user:${loggedUserEmail}`,
        );
      return response.data.data;
    } catch (error) {
      global
        .logger()
        .error(
          `Fail to fetch user info from ${process.env.IDENTITY_MANGER_URL}/v1 server: ${error}`,
        );
      throw new Boom.Boom(error, { statusCode: 500 });
    }
  }

  /**
   * Curl request to auth0 for management token
   * this is post method
   * @return access_token
   */
  static async authM2MToken() {
    global.logger().info('authM2MToken method called!');
    const cache = require('node-file-cache').create();
    try {
      let m2mAccessToken = cache.get('m2m_access_token');
      global
        .logger()
        .info(
          `AuthService.authM2MToken: Retrieved cached token: ${
            m2mAccessToken ? 'exists' : 'not found'
          }`,
        );

      const jwtVerify = await AuthService.verifyJwtToken(m2mAccessToken);
      if (!jwtVerify) {
        global
          .logger()
          .info(
            'AuthService.authM2MToken: Token invalid or expired, generating new token',
          );

        // Validate required environment variables
        if (
          !process.env.M2M_API_URL ||
          !process.env.M2M_CLIENT_ID ||
          !process.env.M2M_CLIENT_SECRET ||
          !process.env.AUTH0_DOMAIN
        ) {
          throw new Error(
            'Missing required M2M environment variables: M2M_API_URL, M2M_CLIENT_ID, M2M_CLIENT_SECRET, AUTH0_DOMAIN',
          );
        }
        const response = await axios({
          method: 'POST',
          url: process.env.M2M_API_URL,
          headers: { 'content-type': 'application/json' },
          data: {
            client_id: process.env.M2M_CLIENT_ID,
            client_secret: process.env.M2M_CLIENT_SECRET,
            audience: `https://${process.env.AUTH0_DOMAIN}/api/v2/`,
            grant_type: 'client_credentials',
          },
        });
        const decoded = jwtDecode(response.data.access_token);
        m2mAccessToken = response.data.access_token;
        const tokenExpiry =
          (new Date(decoded.exp * 1000).getTime() - new Date().getTime()) /
          1000;
        cache.set('m2m_access_token', m2mAccessToken, { life: tokenExpiry });
        global.logger().info(`Generate new M2M token and store in cache.`);
      }
      global.logger().info(`Get machine to machine token`);
      return m2mAccessToken;
    } catch (err) {
      cache.clear();
      global.logger().error(`AuthService.authM2MToken error: ${err}`);
      throw new Boom.Boom(err);
    }
  }

  /**
   * Verify JWT token
   * @param {*} m2mAccessToken
   */
  static async verifyJwtToken(m2mAccessToken) {
    try {
      global.logger().info(`AuthService.verifyJwtToken method called`);

      // Check if token exists and is not null/undefined
      if (
        !m2mAccessToken ||
        m2mAccessToken === 'undefined' ||
        m2mAccessToken === 'null'
      ) {
        global
          .logger()
          .info(`AuthService.verifyJwtToken: No valid token provided`);
        return false;
      }

      const token = jwtDecode(m2mAccessToken);
      if (token && token.exp) {
        // Check if token is expired
        const currentTime = Math.floor(Date.now() / 1000);
        if (token.exp > currentTime) {
          global.logger().info(`AuthService.verifyJwtToken: Token is valid`);
          return true;
        } else {
          global.logger().info(`AuthService.verifyJwtToken: Token is expired`);
          return false;
        }
      } else {
        global
          .logger()
          .info(`AuthService.verifyJwtToken: Invalid token structure`);
        return false;
      }
    } catch (error) {
      global
        .logger()
        .error(`AuthService.verifyJwtToken error: ${error.message}`);
      return false;
    }
  }

  /**
   * THis function is responsible for check given user acl according to fn params.
   * @param {Object Id} userId user id
   * @param {Object Id} projectId project id
   */
  static async checkUserAcl(userId, projectId, module) {
    global.logger().info('checkUserAcl method called!');
    const result = await Permission.findOne(
      { userId, projectId },
      { _id: 1, acl: 1 },
    );
    if (!result) {
      global.logger().error('User has no any permission');
      throw new Boom.Boom(
        `You have not ${module.acl} permission to this company. Please contact to your administration`,
        {
          statusCode: 401,
        },
      );
    }

    const acl = find(result.acl, { moduleName: module.name });

    if (acl && includes(acl.permission, module.acl)) {
      global.logger().info('User have a permissions');
      return true;
    } else {
      global.logger().error('User has no any permission');
      throw new Boom.Boom(
        `You have not ${module.acl} permission to this company. Please contact to your administration`,
        {
          statusCode: 401,
        },
      );
    }
  }

  /**
   * Curl request for user permissions in object of array
   * Call user permission auth0 api with user auth0Id
   * This is get request
   * @param {Hapi request obj} request
   * @return {Array} roles
   */
  static async Auth0UserRoles(auth0Id, m2mToken) {
    try {
      global.logger().info('AuthService.Auth0UserRoles method called!');

      const result = await axios({
        method: 'GET',
        url: `https://${process.env.AUTH0_DOMAIN}/api/v2/users/${auth0Id}/roles`,
        headers: {
          Authorization: `Bearer ${m2mToken}`,
        },
      });

      return get(result, 'data', []);

      // /* Check is permission exist if not return unauthorized error */
      // if (roles.length > 0) {
      //   global.logger().info('Get user auth0 roles!');
      //   return roles;
      // }
      // global.logger().error('User has no any role assign!');
      // throw new Boom.Boom('Unauthorized access', { statusCode: 401 });
    } catch (error) {
      global.logger().error(error, `Error in AuthService.Auth0UserRoles:`);
      throw new Boom.Boom(error, { statusCode: 401 });
    }
  }

  /**
   * This method is responsible for check user has admin role
   * @param {Hapi request obj} request
   * @return {Boolean} true/false
   */
  static async isRoleExist(request, role) {
    try {
      global.logger().info('AuthService.isRoleExist method called!');
      /* Get Auth0 management to management API token */
      const m2mToken = await AuthService.authM2MToken();

      /* Get Auth0 user role by user auth0 id */
      const userRoles = await AuthService.Auth0UserRoles(
        request.user.auth0Id,
        m2mToken,
      );
      if (isEmpty(userRoles)) {
        return false;
      }
      if (find(userRoles, { name: role })) {
        global.logger().info(`User has ${role} role!`);
        return true;
      } else {
        global.logger().error(`User has not assign auth0 ${role} role!`);
        return false;
      }
    } catch (error) {
      global.logger().error(`Error in AuthService.isRoleExist:`, error);
      throw new Boom.Boom(error, { statusCode: 401 });
    }
  }

  /**
   * This method will verify the user token using IM and auth0
   *
   * @param {string} token - user auth token
   * @return {Promise<any>} hapi response
   */
  static async validateUserAuthToken(token) {
    try {
      if (!token) {
        return false;
      }

      /* Decode user auth0 token */
      const decoded = jwtDecode(token);
      if (!decoded.email) {
        return false;
      }

      /* Get user profile data from curl request from activate-im server */
      const user = await AuthService.getUserProfileData(token, decoded.email);

      if (isEmpty(user)) {
        return false;
      }

      return user;
    } catch (error) {
      global
        .logger()
        .error(error, 'Error in AuthService.validateUserAuthToken');
      return {
        return: false,
      };
    }
  }
}

module.exports = AuthService;
