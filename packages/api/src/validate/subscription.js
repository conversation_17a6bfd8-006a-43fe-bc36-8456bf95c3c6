const Joi = require('joi');

exports.Subscription = {
  save: {
    payload: Joi.object({
      planId: Joi.string().required(),
      customerId: Joi.string().required(),
      productPriceId: Joi.string().required(),
      paymentMethodId: Joi.string().required(),
    }),
  },
  billinValidId: {
    payload: Joi.object({
      planId: Joi.string().required(),
      type: Joi.string()
        .valid('trial', 'pro', 'enterprise', 'free', 'legacy')
        .required(),
      email: Joi.string().email().required(),
      priceId: Joi.string(),
    }),
  },
  paymentIntent: {
    payload: Joi.object({
      id: Joi.string().required(),
      priceId: Joi.string(),
    }),
  },
  update: {
    payload: Joi.object({
      status: Joi.string().valid('active', 'canceled', 'expired'),
    }),
    params: Joi.object({
      id: Joi.string().required(),
    }),
  },
  applyCoupon: {
    payload: Joi.object({
      subscriptionId: Joi.string().optional(), // Optional for trial upgrades
      couponCode: Joi.string().required(),
      planId: Joi.string().required(),
      priceId: Joi.string().required(), // Required for creating new subscription
    }),
  },
  removeCoupon: {
    payload: Joi.object({
      subscriptionId: Joi.string().required(),
      planId: Joi.string().required(),
      priceId: Joi.string(),
    }),
  },
};
