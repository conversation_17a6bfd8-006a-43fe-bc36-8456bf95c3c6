<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Widget Integration</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 10px 5px;
        height: 100%;
        background-color: (0, 0%, 0%, 0);
        box-sizing: border-box;
      }
      .widget-container {
        background-color: (0, 0%, 0%, 0);
        height: 100%;
      }
    </style>
  </head>
  <body>
    <div class="widget-container" id="widget-container"></div>

    <!-- Load React and ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Load your widget bundle -->
    <script src="https://im-files-prod-20200924143011010000000001.s3.eu-west-2.amazonaws.com/widget/newpage.bundle.js"></script>

    <script>
        function injectStyles() {
          if (document.getElementById('custom-styles')) return; // prevent duplicates
      
          const style = document.createElement('style');
          style.id = 'custom-styles';
          style.innerHTML = `
            input#message-input {
              font-size: 16px !important;
              font-family: MaisonNeue, sans-serif !important;
              color: #000000 !important;
            }
            input#message-input::placeholder {
              font-size: 16px !important;
              font-family: MaisonNeue, sans-serif !important;
              color: #858585 !important;
            }
          `;
          document.head.appendChild(style);
        }
      
        // Observe widget container for changes and reinject styles
        const widgetContainer = document.getElementById('widget-container');
        const observer = new MutationObserver(() => {
          injectStyles();
        });
      
        observer.observe(widgetContainer, { childList: true, subtree: true });
      
        document.addEventListener('DOMContentLoaded', function () {
          function tryInitWidget() {
            if (window.widget && typeof window.widget.init === 'function') {
              window.widget.init({
                widgetCallBack: function (err, data) {
                  console.log('Widget callback:', err, data);
                  injectStyles();
                },
                organizationId: '67d17995cec95487f272df18',
                agentId: '6825c5f2cec95487f27c2bda',
                styles: {
                  footer: {
                    // width: '100%',
                    display: 'flex',
                    border: 'none',
                    // padding: '0px 0px',
                    // background: '#fff',
                    position: 'sticky',
                    bottom: 0,
                    zIndex: 1,
                  },
                  messageContainer: {
                    sent: {  marginRight: '8px' },
                    received: { marginRight: '8px' },
                  },
                  message: {
                    sent: {
                      background: '#FE863D',
                      borderRadius: '24px',
                      color: '#fff',
                      fontSize: '16px',
                    },
                    received: {
                      background: '#FFFFFF',
                      borderRadius: '24px',
                      color: '#000000',
                      fontSize: '16px',
                    },
                  },
                  button: {
                    background: '#FE863D',
                    alignItems: 'center',
                    fontSize: '16px',
                    justifyContent: 'center',
                    disabled: {
                      background: '#FE863D',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px',
                      opacity: '0.4',
                    },
                  },
                },
                discardCallBack: function () {
                  console.log('Discarded');
                },
                discardButtonText: 'Discard',
                callbackButtonText: 'Send',
              });
            } else {
              setTimeout(tryInitWidget, 100);
            }
          }
          tryInitWidget();
        });
      </script>      
  </body>
</html>
