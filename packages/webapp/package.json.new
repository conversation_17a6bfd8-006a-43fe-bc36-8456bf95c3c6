{"name": "smash-webapp", "version": "8.1.1", "scripts": {"dev": "next", "build": "next build", "export": "next export", "start": "next start", "start:ci": "next start", "test": "cypress open", "cy:run": "cypress run --browser chrome --headless", "test:cypress": "start-server-and-test start http://localhost:3000 test:ci", "test:coverage": "jest --coverage --watchAll=false", "test:watch": "jest --watch", "test:ci": "eslint .", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.+(js|yml|yaml|json)\"", "storybook": "start-storybook -p 6006 -s ./public", "build-storybook": "build-storybook -s ./public -c .storybook -o .out_storybook", "stylelint": "stylelint '**/*.scss' '**/*.scss'"}, "dependencies": {"@ckeditor/ckeditor5-build-multi-root": "^41.3.1", "@ckeditor/ckeditor5-react": "^6.3.0", "@mui/material": "^5.15.4", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^1.9.5", "@sentry/nextjs": "^8.20.0", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.7.0", "@uppy/aws-s3": "^3.6.0", "@uppy/core": "^3.8.0", "@uppy/dashboard": "^3.7.1", "@uppy/drag-drop": "^3.0.3", "@uppy/file-input": "^3.0.4", "@uppy/image-editor": "^2.4.0", "@uppy/progress-bar": "^3.0.4", "@uppy/react": "^3.2.1", "@vimeo/player": "^2.20.1", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "bootstrap": "^4.6.2", "chart.js": "^4.4.1", "classnames": "^2.5.1", "dotenv": "^16.3.1", "fslightbox-react": "^1.7.6", "history": "^5.3.0", "i18next": "^22.4.15", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "logrocket": "^7.0.0", "mixpanel-browser": "^2.56.0", "moment": "^2.29.4", "next": "^15.2.4", "next-i18next": "^13.2.2", "next-redux-wrapper": "^8.1.0", "qrcode.react": "^3.1.0", "query-string": "^8.1.0", "react": "^18.2.0", "react-bootstrap": "^1.6.8", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.2.0", "react-dropdown": "^1.11.0", "react-ga4": "^2.1.0", "react-i18next": "^12.2.2", "react-inlinesvg": "^4.1.3", "react-load-script": "^0.0.6", "react-movable": "^3.0.4", "react-multi-carousel": "^2.8.4", "react-paginate": "^8.2.0", "react-pdf": "^7.6.0", "react-phone-input-2": "^2.15.1", "react-redux": "^8.1.3", "react-s3-uploader": "^5.0.0", "react-select": "^5.8.0", "react-tag-input": "^6.8.1", "react-toastify": "^9.1.3", "react-tooltip": "^5.25.0", "redux": "^4.2.1", "redux-form": "^8.3.10", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "sharp": "^0.33.1", "socket.io-client": "^4.7.2", "store": "^2.0.12", "validator": "^13.11.0"}, "license": "ISC", "main": "store.js", "keywords": [], "author": "", "description": "", "devDependencies": {"@babel/core": "^7.23.6", "@next/eslint-plugin-next": "^14.0.4", "autoprefixer": "^10.4.19", "babel-loader": "^9.1.3", "css-loader": "^6.11.0", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-prettier": "^5.1.2", "i18next-fs-backend": "^2.3.1", "postcss": "^8.4.38", "prettier": "^3.1.1", "react-calendly": "^4.3.1", "sass": "^1.69.5", "sass-loader": "^13.3.3"}}