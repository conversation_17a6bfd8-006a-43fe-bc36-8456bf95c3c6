# ENV vars for test
export  IM_API_BASE_URL=http://localhost:8000/
export  SMASH_API_BASE_URL=http://localhost:9000
export  LOGIN_URL=http://localhost:3000/
export  MAX_FILE_SIZE_ALLOWED=10000000
export  LINKEDIN_BASE_URL=https://www.linkedin.com/oauth/v2/authorization?
export  LINKEDIN_RESPONSE_TYPE=code
export  LINKEDIN_CLIENT_ID=XXXXXXXXXXXXXXX
export  LINKEDIN_REDIRECT_URI=http://localhost:3000/verify/linkedin/callback
export  LINKEDIN_SCOPE=r_emailaddress r_liteprofile w_member_social
export  API_BASE_URL_GOOGLE_MAP=https://maps.googleapis.com/maps/api/js?
export  API_GOOGLE_MAP_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
export  NEXT_PUBLIC_URLS=['/','/_error','/404','/signIn','/verify','/project/snap/[snapshotHash]','/decisionMaker/signUp','/collaborator/signUp','/collaborator/verify/[id]','/callouts/public','/callouts/public/[id]/[referrer]']
export  LOGROCKET_KEY="some api key"
export  LOGROCKET_ENABLED=false
export  LIST_API_LIMIT=1000
export  SNAP_BASE_URL=http://localhost:3000/
export  GTM_KEY=SMJSO
export  CRISP_ID="6469a2a7-2a84-4e7b-86b7-d8908jsa"

