/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import { get } from 'lodash';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import Icon from 'sharedComponents/Icon/Icon';
import { ProgressBar } from 'react-bootstrap';
import Style from '../../style/projectDashboard.module.scss';

// This component is used for upload file.
class FileInputField extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      progress: 0,
      isProgress: false,
      preview: null,
      style: '',
    };
  }

  // Get uploaded video urls.
  getUploadedUrl = (uploadedFile) => {
    const { input } = this.props;
    this.setState({ isProgress: false, style: '' });
    input.onChange(uploadedFile.file);
  };

  // Set progress bar handler.
  onProgressHandler = (percentage) => {
    this.setState({ progress: percentage });
  };

  // This method call when file is uploaded.
  onPreprocessHandler = (file) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onloadend = function () {
      this.setState({
        preview: reader.result,
        style: '--preview',
        isProgress: true,
      });
    }.bind(this);
  };

  render() {
    const { preview, style, progress, isProgress } = this.state;
    const {
      input,
      meta: { touched, error },
      projectPreviewData,
    } = this.props;
    let imageUrl = input.value;
    if (preview) {
      imageUrl = preview;
    }
    return (
      <>
        <FileUpload
          accept={['jpg', 'png', 'jpeg']}
          onSuccessHandler={this.getUploadedUrl}
          onProgressHandler={this.onProgressHandler}
          onPreprocessHandler={this.onPreprocessHandler}
          cropShape="rect"
        />
        {imageUrl ? (
          <>
            <div className={`imagePreviewArea ${style}`}>
              <img src={imageUrl} alt="preview" />
            </div>
            {isProgress && (
              <ProgressBar
                now={progress}
                label={`${progress}%`}
                className="progressBar"
              />
            )}
          </>
        ) : (
          <div className="imgUpload --circle --cast">
            <Icon
              iconSize="48px"
              color={
                get(projectPreviewData, 'theme') === 'light'
                  ? '#414141'
                  : '#e4e4e4'
              }
              icon={UploadButtonSvgPath}
            />
          </div>
        )}
        {touched && error && (
          <p abc="xyz" name={error} className={Style.validationError}>
            {error}
          </p>
        )}
      </>
    );
  }
}

FileInputField.propTypes = {
  input: PropTypes.object.isRequired,
  meta: PropTypes.object.isRequired,
  projectPreviewData: PropTypes.object.isRequired,
};

export default FileInputField;
