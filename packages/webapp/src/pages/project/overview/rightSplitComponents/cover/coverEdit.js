/* eslint-disable @next/next/no-img-element */
import React from 'react';
import <PERSON><PERSON><PERSON>ield from 'sharedComponents/renderfield';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { get, assign } from 'lodash';
import Button from 'sharedComponents/Button/button';
import { required } from 'validation/commonValidation';
import { preventEnterKey } from 'src/utils/helper';
import { Field, reduxForm } from 'redux-form';
import SimpleDropDown from 'sharedComponents/simpleDropDown';
import DropDown from 'sharedComponents/dropDown/dropdown';
import ReactModal from 'sharedComponents/Modal/modal';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import TrashIconSvgPath from 'svgpath/TrashIconSvgPath';
import MainStyles from '../../style/projectDashboard.module.scss';
import Style from './style/cover.module.scss';

// Project cover data edit form component
class CoverEdit extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showDropdown: '',
      isShowModal: false,
    };
    this.fileUploadRef = React.createRef();
  }

  componentDidMount() {
    const { projectPreviewData, initialize, setCoverImage } = this.props;
    get(projectPreviewData, 'cover') &&
      initialize({
        title: projectPreviewData.cover.title || '',
        producer: projectPreviewData.cover.producer || '',
        writer: projectPreviewData.cover.writer || '',
        director: projectPreviewData.cover.director || '',
      });
    setCoverImage(projectPreviewData.cover.coverPic || null);

    // Style the file input to be invisible
    if (this.fileUploadRef.current) {
      const fileInput =
        this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.top = '-9999px';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
      }
    }
  }

  // to close the edit section
  cancel = () => {
    const { cancel, setSaveButtonState, setCoverImageUploadStatus } =
      this.props;
    setSaveButtonState(false);
    setCoverImageUploadStatus(false);
    cancel();
  };

  // submit form values
  submitProjectForm = (values) => {
    const { updateCoverInfo, router, coverImg } = this.props;
    const updatedData = assign({}, values, { coverPic: coverImg });
    const id = router.asPath.split('/').pop();
    updateCoverInfo(updatedData, id, 'close');
  };

  // save data which is added by the user
  saveData = () => {
    this.form.dispatchEvent(new Event('submit', { cancelable: true }));
  };

  // open dropdown to add cover image
  toggleDropdown = () => {
    const { showDropdown } = this.state;
    if (showDropdown) {
      this.setState({
        showDropdown: '',
      });
    } else {
      this.setState({
        showDropdown: 'visibleDropdown',
      });
    }
  };

  // method to remove uploaded cover image
  removeCoverPicHandler = () => {
    const { deleteFile, router, setCoverImage } = this.props;
    const id = router.asPath.split('/').pop();
    deleteFile(id, 'coverPic');
    setCoverImage(null);
    this.setState({
      isShowModal: false,
    });
  };

  // method to close modal
  closeModalHandler = () => {
    this.setState({
      isShowModal: false,
    });
  };

  // method to open modal
  removeCover = () => {
    this.setState({
      isShowModal: true,
    });
  };

  // method to get url of image
  getUploadedUrl = () => {
    // Trigger the file input directly
    if (this.fileUploadRef.current) {
      const fileInput =
        this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  render() {
    const {
      t,
      handleSubmit,
      producerList,
      directorList,
      writerList,
      isDisable,
      projectPreviewData,
      setCoverImage,
      coverImg,
      // coverImgUploadStatus,
      setCoverImageUploadStatus,
    } = this.props;
    const { showDropdown, isShowModal } = this.state;
    const dropdownOptions = [
      {
        icon: UploadButtonSvgPath,
        uploadControl: false,
        handler: this.getUploadedUrl,
        value: 'ADD COVER',
      },
      {
        icon: TrashIconSvgPath,
        uploadControl: false,
        handler: this.removeCover,
        value: 'REMOVE COVER',
      },
    ];
    return (
      <>
        <div>
          <ReactModal
            modalShow={isShowModal}
            title="Dou you want to remove this cover image?"
            body="This action cannot be undone."
            successBtnText="Yes! Remove"
            closeBtnText="Cancel"
            modalSize="md"
            successCallback={this.removeCoverPicHandler}
            closeCallback={this.closeModalHandler}
          />

          <div className="container-fluid">
            <div className="row justify-content-center">
              <div
                className={`p-0 ${Style.coverEditHeader} ${Style.coverBackground} col-md-12`}
                style={
                  coverImg
                    ? {
                        background: `url(${coverImg})`,
                      }
                    : {}
                }
              >
                <form
                  onSubmit={handleSubmit(this.submitProjectForm)}
                  ref={(n) => {
                    this.form = n;
                  }}
                  onKeyDown={preventEnterKey}
                >
                  <div className="row">
                    <div className="col-md-12">
                      <div className="flex-wrap d-flex justify-content-center p-2 ">
                        <label
                          className={`${Style.headers}`}
                          data-cy="producerHeadingEdit"
                        >
                          {t('common:projectCreate.preview.producedBy')}
                        </label>
                        <div className={`${Style.producerDropDown} ml-2`}>
                          <Field
                            id="producerList"
                            name="producer"
                            component={SimpleDropDown}
                            options={producerList}
                            type="updateCover"
                            projectPreviewData={projectPreviewData}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`${Style.hr}`} />
                  <div className={`${Style.titleContainer}`}>
                    <div className="row">
                      <div className="col-md-12 text-center mb-3">
                        <div>
                          <button
                            type="button"
                            onClick={this.toggleDropdown}
                            onBlur={this.toggleDropdown}
                            className={Style.dropdownBtn}
                            data-cy="uploadImageCover"
                          >
                            {`${coverImg ? 'Edit' : 'Add'} Cover Photo`}
                            <img
                              src={
                                get(projectPreviewData, 'theme') === 'light'
                                  ? '/assets/svg/icon-downarrow-dark.svg'
                                  : '/assets/png/DownArrow.png'
                              }
                              alt="profile"
                              className={
                                get(projectPreviewData, 'theme') === 'light'
                                  ? 'ml-2 h-75'
                                  : 'ml-2'
                              }
                              data-cy="creativeImage"
                            />
                          </button>

                          <DropDown
                            options={dropdownOptions}
                            dropdownclass={`${Style.dropdownPos} ${Style[showDropdown]}`}
                          />
                        </div>
                      </div>
                      <div className="col-md-12">
                        <div className="mb-3" id="titleEdit">
                          <Field
                            component={RenderField}
                            name="title"
                            placeholder={t(
                              'common:projectCreate.create.form.title',
                            )}
                            validate={[required]}
                            type="text"
                            data-cy="titleEdit"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={`${Style.hr}`} />
                  <div className="col-md-12 col-lg-12 flex-wrap row  pr-0 pl-0">
                    <div className="col-md-6 col-sm-12 pr-0 pl-0">
                      <div className={`${Style.director}`}>
                        <p
                          className={`${Style.headers}`}
                          data-cy="directorHeadingEdit"
                        >
                          {t('common:projectCreate.preview.directedBy')}
                        </p>

                        <div className={`${Style.dropDownWidth} ml-2`}>
                          <Field
                            id="directorList"
                            name="director"
                            component={SimpleDropDown}
                            options={directorList}
                            type="updateCover"
                            data-cy="directorDropdown"
                            projectPreviewData={projectPreviewData}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6 col-sm-12">
                      <div className={`${Style.writer}`}>
                        <p
                          className={`${Style.headers}`}
                          data-cy="writerHeadingEdit"
                        >
                          {t('common:projectCreate.preview.writtenBy')}
                        </p>
                        <div className={`${Style.dropDownWidth} ml-2`}>
                          <Field
                            id="writerList"
                            name="writer"
                            component={SimpleDropDown}
                            options={writerList}
                            type="updateCover"
                            projectPreviewData={projectPreviewData}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className={`${MainStyles.btnRow} d-none`}>
                    <div className={`${MainStyles.btnBgDrag} row`}>
                      <div className={`${MainStyles.btnCont}`}>
                        <div className={`${MainStyles.smallButton}`}>
                          <Button
                            btntype="submit"
                            id="coverSave"
                            size="small"
                            customClass={
                              !isDisable ? '--primary' : '--disabled'
                            }
                            buttonValue="SAVE"
                            // clickHandler={() => this.saveData()}
                          />
                        </div>
                      </div>
                      <div className={`${Style.btnCont}`}>
                        <div className={`${Style.smallButton}`}>
                          <Button
                            btntype="button"
                            id="coverCancel"
                            size="small"
                            customClass={
                              !isDisable ? '--primary' : '--disabled'
                            }
                            buttonValue="CANCEl"
                            clickHandler={() => this.cancel()}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* FileUpload component visible but file input styled to be invisible */}
        <div
          ref={this.fileUploadRef}
          data-cy="uploadImageArtwork"
          style={{
            position: 'relative',
            width: '1px',
            height: '1px',
            overflow: 'hidden',
          }} // Keep component in DOM but minimal size
        >
          <FileUpload
            name="coverImage"
            accept={['jpeg', 'jpg', 'png']}
            onSuccessHandler={(result) => {
              const url = result.coverImage?.url || result.coverImage;
              setCoverImage(url);
              setCoverImageUploadStatus(false);
            }}
            onProgressHandler={() => setCoverImageUploadStatus(true)}
            onPreprocessHandler={() => setCoverImageUploadStatus(true)}
            enableCropping={true}
            cropShape="rect"
            cropHeight={320}
            className="uploadStyleBtn"
          />
        </div>
      </>
    );
  }
}
CoverEdit.propTypes = {
  t: PropTypes.func.isRequired,
  router: PropTypes.func.isRequired,
  updateCoverInfo: PropTypes.bool.isRequired,
  producerList: PropTypes.bool.isRequired,
  writerList: PropTypes.bool.isRequired,
  directorList: PropTypes.bool.isRequired,
  projectPreviewData: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
  cancel: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  isDisable: PropTypes.bool.isRequired,
  setSaveButtonState: PropTypes.func.isRequired,
  deleteFile: PropTypes.func.isRequired,
  setCoverImage: PropTypes.func.isRequired,
  coverImg: PropTypes.string.isRequired,
  setCoverImageUploadStatus: PropTypes.func.isRequired,
  coverImgUploadStatus: PropTypes.bool.isRequired,
};

export default reduxForm({
  form: 'editForm',
})(withTranslation('common')(CoverEdit));
