import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Icon from 'sharedComponents/Icon/Icon';
import Modal from 'sharedComponents/Modal/modal';
import InfoIcon from 'svgpath/InfoIconSvgPath';
import Button from 'sharedComponents/Button/button';
import EditIconSvgPath from 'svgpath/EditiconSvgPath';
import { pushToDataLayer } from 'lib/commonUtil';
import { get, forEach } from 'lodash';
import ShowImage from './showImages';
import EditImage from './editImages';
import Style from './style/artwork.module.scss';
import style from '../../style/projectDashboard.module.scss';

class projectArtworkContainer extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showDescription: false,
    };
  }

  componentDidMount() {
    const { projectPreviewData, setImageData } = this.props;
    const artWork = get(projectPreviewData, 'artWork', []);
    // method to set image data coming from api
    setImageData(artWork);
  }

  // method to open edit section of image artwork
  editFormToggleHandler = () => {
    const { setSaveButtonStateArtWork } = this.props;
    setSaveButtonStateArtWork(true);
    pushToDataLayer({
      action: 'getty click edit',
      category: 'Project moodboard',
      label: 'Click edit moodboard section',
    });
  };

  // method to close edit section of image artwork
  closeEdit = () => {
    const { setSaveButtonStateArtWork } = this.props;
    setSaveButtonStateArtWork(false);
    this.removeHashFromUrl();
  };

  // method to save the data of uploaded images
  saveData = () => {
    const { updateProjectArtwork } = this.props;
    updateProjectArtwork('close', null, null, null);
    this.removeHashFromUrl();
  };

  // method to remove the data from url
  removeHashFromUrl = () => {
    const currentUrl = window.location.href;
    const url = currentUrl.split('#');
    window.history.pushState(currentUrl, 'Overview', url[0]);
  };

  // method to send all the uploaded image data to api
  setImage = (data) => {
    const {
      setImageData,
      updateProjectArtwork,
      projectPreviewData,
      imageList,
    } = this.props;
    const projectId = get(projectPreviewData, '_id');
    
    // Handle Getty image data
    if (get(data, 'id')) {
      const imagesArray = [];
      const images = {};
      images.url = data.display_sizes[0].uri;
      images.title = data.title;
      images.disc = data.title;
      images.source = 'getty';
      imagesArray.push(images);

      forEach(imageList, (item) => {
        imagesArray.push(item);
      });

      setImageData(imagesArray);
      updateProjectArtwork(
        'save',
        imagesArray,
        projectId,
        'artWorkImage',
        'Image added',
      );
    } 
    // Handle array of image objects (from existing functionality)
    else if (Array.isArray(data)) {
      const images = data.map((item) => {
        return {
          url: item.uploadURL,
          title: item.meta.name,
          disc: item.meta.caption,
          source: item.source,
          id: item._id,
        };
      });

      forEach(imageList, (item) => {
        images.push(item);
      });

      setImageData(images);
      updateProjectArtwork(
        'save',
        images,
        projectId,
        'artWorkImage',
        'Image added',
      );
    }
    // Handle single URL string (from FileUpload)
    else if (typeof data === 'string') {
      const imagesArray = [];
      const newImage = {
        url: data,
        title: 'Uploaded Image',
        disc: 'Uploaded Image',
        source: 'fileupload',
        _id: Date.now().toString(), // Generate temporary ID
      };
      imagesArray.push(newImage);

      forEach(imageList, (item) => {
        imagesArray.push(item);
      });

      setImageData(imagesArray);
      updateProjectArtwork(
        'save',
        imagesArray,
        projectId,
        'artWorkImage',
        'Image added',
      );
    }
  };

  // This function set modal status false.
  closeModal = () => {
    this.setState({
      showDescription: false,
    });
  };

  // this method is used for modal body
  body = () => {
    const { t } = this.props;
    return (
      <div>
        <div className="text-left">
          <p className={style.modalBody}>
            {t('common:projects.overview.basicInfo.artwork')}
          </p>
        </div>
      </div>
    );
  };

  render() {
    const {
      t,
      isDisable,
      setSaveButtonStateArtWork,
      showButtonArtwork,
      removeSectionItems,
      projectPreviewData,
      imageList,
      setImageData,
      updateProjectArtwork,
      updateCoverInfo,
      userData,
      iconHide,
      onOffArtworkStatus,
    } = this.props;
    const { showDescription } = this.state;
    return (
      <div
        className={`${
          onOffArtworkStatus === 'lock' ? 'sectionHide' : ''
        } container-fluid`}
      >
        {showDescription && (
          <div>
            <Modal
              modalShow={showDescription}
              title="Mood Board"
              className="darkbackGround"
              body={this.body()}
              closeCallback={() => this.closeModal()}
              isShowCrossBtn
              titleClass={style.modalTitle}
              closeBtnClass={`${style.buttonContainer}`}
              divider
              bodyClass="para"
            />
          </div>
        )}
        <div className={`${Style.desRow} d-none row justify-content-center`}>
          <div
            className="mr-4 mt-5"
            onClick={this.editFormToggleHandler}
            id="artWorkPencilBtn"
          >
            <Icon iconSize="28px" color="#e4e4e4" icon={EditIconSvgPath} />
          </div>
        </div>

        <div className={`${Style.btnRowDesc} d-none`}>
          <div className={`${Style.btnBgDrag} flex-wrap d-flex p-0`}>
            <div className={`${Style.btnCont}`}>
              <div className={`${Style.smallButton}`}>
                <Button
                  btntype="button"
                  id="artWorkSave"
                  size="small"
                  customClass={!isDisable ? '--primary' : '--disabled'}
                  buttonValue="SAVE"
                  clickHandler={() => this.saveData()}
                />
              </div>
            </div>
            <div className={`${Style.btnCont}`}>
              <div className={`${Style.smallButton}`}>
                <Button
                  btntype="button"
                  id="artWorkCancel"
                  size="small"
                  customClass={!isDisable ? '--primary' : '--disabled'}
                  buttonValue="CANCEL"
                  clickHandler={() => this.closeEdit()}
                />
              </div>
            </div>
          </div>
        </div>

        <div className={`${Style.iconContainer} row `}>
          <div className={`${style.headingContainer} d-flex flex-row`}>
            <div className={`${style.rectangle} ${Style.rectangleWidth}`}>
              <div className={`${style.headingText}`} data-cy="artworkHeading">
                MOOD BOARD
              </div>
            </div>
            {!iconHide === true && (
              <div
                className={`${style.headingContainer} ml-2`}
                onClick={() => this.setState({ showDescription: true })}
              >
                <Icon
                  iconSize="24px"
                  color={
                    get(projectPreviewData, 'theme') === 'light'
                      ? '#414141'
                      : '#e4e4e4'
                  }
                  icon={InfoIcon}
                />
              </div>
            )}
          </div>
        </div>

        {!showButtonArtwork ? (
          <ShowImage
            isDisable={isDisable}
            setSaveButtonStateArtWork={setSaveButtonStateArtWork}
            showButtonArtwork={showButtonArtwork}
            imageList={imageList}
            setImageData={setImageData}
          />
        ) : (
          <EditImage
            t={t}
            imageList={imageList}
            setImage={this.setImage}
            removeSectionItems={removeSectionItems}
            projectPreviewData={projectPreviewData}
            setImageData={setImageData}
            updateProjectArtwork={updateProjectArtwork}
            updateCoverInfo={updateCoverInfo}
            userData={userData}
          />
        )}
      </div>
    );
  }
}

projectArtworkContainer.propTypes = {
  t: PropTypes.func.isRequired,
  isDisable: PropTypes.bool.isRequired,
  showButtonArtwork: PropTypes.bool.isRequired,
  setSaveButtonStateArtWork: PropTypes.func.isRequired,
  projectPreviewData: PropTypes.func.isRequired,
  updateProjectArtwork: PropTypes.func.isRequired,
  router: PropTypes.func.isRequired,
  imageList: PropTypes.func.isRequired,
  setImageData: PropTypes.func.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
  updateCoverInfo: PropTypes.func.isRequired,
  snapStatus: PropTypes.bool.isRequired,
  userData: PropTypes.func.isRequired,
  iconHide: PropTypes.bool.isRequired,
  onOffArtworkStatus: PropTypes.string.isRequired,
};

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

export default projectArtworkContainer;
