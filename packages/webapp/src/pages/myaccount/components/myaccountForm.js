/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { getFormValues, isValid } from 'redux-form';
import Button from 'sharedComponents/Button/button';
import { withTranslation } from 'react-i18next';
import Icon from 'sharedComponents/Icon/Icon';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import TrashIconSvgPath from 'svgpath/TrashIconSvgPath';
import { get } from 'lodash';
import { setLocation } from 'reducer/user';
import DropDown from 'sharedComponents/dropDown/dropdown';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import OrgForm from './organisationForm';
import InterestForm from './interestForm';
import SocialMediaForm from './socialMediaForm';
import Privacy from './privacy';
import ContactForm from './contactForm';
import UserProfileForm from './userProfileForm';
import Style from '../style/myaccount.module.scss';

/*  this component is used for update the user profile
 * such as user's name , user's phone number and user's profile picture.
 */
class MyAccount extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      btnValue: 'SAVE',
      profileImage: null,
      organisationLogo: null,
    };
    this.profileFileUploadRef = React.createRef();
    this.logoFileUploadRef = React.createRef();
  }

  // Handle icon click to trigger file upload (similar to editCover component)
  handleProfileIconClick = () => {
    if (this.profileFileUploadRef.current) {
      const fileInput =
        this.profileFileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  handleLogoIconClick = () => {
    if (this.logoFileUploadRef.current) {
      const fileInput =
        this.logoFileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  componentDidMount() {
    const { userData, setLocation } = this.props;
    const profileImage = get(userData, 'profile.profileImage', false);
    const organisationLogo = get(userData, 'profile.organisationLogo', false);

    if (profileImage) {
      this.setState({
        profileImage,
      });
    }

    if (organisationLogo) {
      this.setState({
        organisationLogo,
      });
    }

    setLocation(get(userData, 'profile.city', {}));
  }

  // This method will call when user upload profile image.
  getUploadedUrl = (uploadedFile, type) => {
    const { updateUserProfile, userData } = this.props;
    const profile = get(userData, 'profile');
    if (profile) {
      const updatedProfile = {
        profile: {},
      };

      if (type === 'profileImage') {
        updatedProfile.profile.profileImage = uploadedFile.file.url;
        this.setState({ profileImage: uploadedFile.file.url });
      } else if (type === 'organisationLogo') {
        updatedProfile.profile.organisationLogo = uploadedFile.file.url;
        this.setState({ organisationLogo: uploadedFile.file.url });
      }

      updateUserProfile(updatedProfile, 'update');
    }
  };

  isFormValid = () => {
    const {
      isValidUserProfileForm,
      isValidOrganisationForm,
      isValidInterestForm,
      isValidSocialMediaForm,
      isValidPrivacyForm,
      isValidContactForm,
    } = this.props;
    if (
      isValidUserProfileForm &&
      isValidOrganisationForm &&
      isValidInterestForm &&
      isValidSocialMediaForm &&
      isValidPrivacyForm &&
      isValidContactForm
    ) {
      return true;
    }
    return false;
  };

  // Submit event handler.
  saveData = () => {
    const {
      orgFormValues,
      interestFormValues,
      socialMediaFormValues,
      privacyFormValues,
      contactFormValues,
      userProfileFormValues,
      updateUserProfile,
      location,
      interests,
      editModeStatus,
      setTagData,
    } = this.props;
    // if form is not valid then return.
    if (!this.isFormValid()) {
      return;
    }

    const data = [];
    interests.forEach((item) => {
      if (item.new) {
        data.push({ text: item.text, id: item.id });
        delete item.new;
      }
    });
    const orgSize = get(orgFormValues, 'organisationSize');
    const updatedData = {
      profile: {
        name: {
          firstName: userProfileFormValues.firstName,
          lastName: userProfileFormValues.lastName,
          fullName: `${userProfileFormValues.firstName} ${userProfileFormValues.lastName}`,
        },
        occupation: userProfileFormValues.occupation,
        occupationType: userProfileFormValues.occupationType,
        otherOccupation: userProfileFormValues.otherOccupation,
        IMBDlink: userProfileFormValues.imdbUrl,
        city: location,
        jobTitle: orgFormValues.jobTitle,
        organisation: orgFormValues.organisation,
        organisationType: orgFormValues.organisationType,
        organisationSize: orgSize ? JSON.parse(orgSize) : '',
        otherOrganisation: orgFormValues.otherOrganisation,
        discovererProfile: orgFormValues.discovererProfile,
        privacy: privacyFormValues.privacy,
        interests: interestFormValues.tags,
        socialMediaUrls: {
          imdb: socialMediaFormValues.imdb,
          twitter: socialMediaFormValues.twitter,
          linkedin: socialMediaFormValues.linkedin,
          dot: socialMediaFormValues.dot,
          instagram: socialMediaFormValues.instagram,
          facebook: socialMediaFormValues.facebook,
          tiktok: socialMediaFormValues.tiktok,
        },
        contact: {
          email: contactFormValues.email,
          phone: contactFormValues.phone,
          sms: contactFormValues.sms,
        },
      },
    };

    updateUserProfile(updatedData, 'update');
    editModeStatus();
    setTagData(data);
  };

  // This method will call when user removes profile image.
  removeAvatar = (type) => {
    const { removeProfilePicture, updateUserProfile } = this.props;

    if (type === 'profileImage') {
      removeProfilePicture();
      this.setState({ profileImage: null });
      updateUserProfile(
        {
          profile: {
            profileImage: null,
          },
        },
        'update',
      );
    } else if (type === 'organisationLogo') {
      this.setState({ organisationLogo: null });
      updateUserProfile(
        {
          profile: {
            organisationLogo: null,
          },
        },
        'update',
      );
    }
  };

  render() {
    const {
      t,
      userData,
      setInterests,
      interests,
      tagList,
      setTagData,
      setLocation,
    } = this.props;
    const { btnValue, profileImage, organisationLogo } = this.state;

    const dropdownOptions = [
      {
        icon: TrashIconSvgPath,
        uploadControl: false,
        handler: () => this.removeAvatar('profileImage'),
        value: 'Remove Avatar',
      },
    ];

    const logoDropdownOptions = [
      {
        icon: TrashIconSvgPath,
        uploadControl: false,
        handler: () => this.removeAvatar('organisationLogo'),
        value: 'Remove Logo',
      },
    ];

    return (
      <>
        <div className={`${Style.mainContainer}`}>
          <div
            className={`${Style.detailsContainer} bg-secondary p-3 p-sm-3 p-md-48 p-lg-48 `}
          >
            <h2 className="text-primary">users details</h2>
            <div className="row m-0 p-0">
              <div className="text-center mt-0 mt-sm-0 mt-md-4 mt-lg-4 position-relative">
                <section
                  data-cy="logoContainer"
                  className={`${Style.avatarContainer} text-center m-0 position-relative`}
                >
                  {profileImage && (
                    <img
                      src={profileImage}
                      height="160px"
                      width="160px"
                      className={`${Style.avatar} ${Style.img} rounded-circle mb-md-5 mb-lg-5 mb-0 mb-sm-0`}
                      alt=""
                    />
                  )}

                  {/* Upload icon centered within the image */}
                  {profileImage && (
                    <div
                      onClick={this.handleProfileIconClick}
                      className={Style.profileImageUploadIcon}
                    >
                      <Icon
                        iconSize="48px"
                        svgclass="--low-emphasis"
                        icon={UploadButtonSvgPath}
                        style={{ color: 'white' }}
                      />
                    </div>
                  )}
                </section>
                <DropDown
                  options={dropdownOptions}
                  dropdownclass={Style.dropdownPos}
                />
                {!profileImage && (
                  <div className="upload-btn-wrapper-img">
                    <div className="imgUpload --circle --border mt-md-5 mt-lg-5 m-0 m-sm-0">
                      <Icon
                        iconSize="48px"
                        svgclass="--low-emphasis"
                        icon={UploadButtonSvgPath}
                      />
                      <FileUpload
                        isOriginalName
                        name="file"
                        accept={['jpeg', 'png', 'jpg', 'JPEG', 'PNG', 'JPG']}
                        onSuccessHandler={(file) =>
                          this.getUploadedUrl(file, 'profileImage')
                        }
                        onProgressHandler={this.progressHandler}
                      />
                    </div>
                  </div>
                )}

                {/* Hidden FileUpload for when image exists - similar to editCover */}
                {profileImage && (
                  <div
                    ref={this.profileFileUploadRef}
                    className={Style.profileUploader}
                  >
                    <FileUpload
                      isOriginalName
                      name="file"
                      accept={['jpeg', 'png', 'jpg', 'JPEG', 'PNG', 'JPG']}
                      onSuccessHandler={(file) =>
                        this.getUploadedUrl(file, 'profileImage')
                      }
                      onProgressHandler={this.progressHandler}
                    />
                  </div>
                )}
              </div>
              <div className="col-md-10 m-0 p-0 p-sm-0 p-md-3 p-lg-3">
                <UserProfileForm
                  userData={userData}
                  setLocation={setLocation}
                />
              </div>
            </div>
          </div>
          <div className="mt-20 mt-sm-20 mt-md-5 mt-lg-5" />
          <div
            className={`${Style.detailsContainer} bg-secondary p-3 p-sm-3 p-md-48 p-lg-48 `}
          >
            <h2 className="text-primary">organization details</h2>
            <div className="row m-0 p-0">
              <div className="text-center mt-0 mt-sm-0 mt-md-4 mt-lg-4 position-relative">
                <section
                  data-cy="logoContainer"
                  className={`${Style.avatarContainer} text-center position-relative`}
                >
                  {organisationLogo && (
                    <img
                      src={organisationLogo}
                      height="160px"
                      width="160px"
                      className={`${Style.avatar} ${Style.img} rounded-circle mb-md-5 mb-lg-5 mb-0 mb-sm-0`}
                      alt=""
                    />
                  )}

                  {/* Upload icon centered within the logo */}
                  {organisationLogo && (
                    <div
                      onClick={this.handleLogoIconClick}
                      className={Style.profileImageUploadIcon}
                    >
                      <Icon
                        iconSize="48px"
                        svgclass="--low-emphasis"
                        icon={UploadButtonSvgPath}
                        style={{ color: 'white' }}
                      />
                    </div>
                  )}
                </section>
                <DropDown
                  options={logoDropdownOptions}
                  dropdownclass={Style.dropdownPos}
                />
                {!organisationLogo && (
                  <div className="upload-btn-wrapper-img">
                    <div className="imgUpload --circle --border mt-md-5 mt-lg-5 m-0 m-sm-0">
                      <Icon
                        iconSize="48px"
                        svgclass="--low-emphasis"
                        icon={UploadButtonSvgPath}
                      />
                      <FileUpload
                        isOriginalName
                        name="file"
                        accept={['jpeg', 'png', 'jpg', 'JPEG', 'PNG', 'JPG']}
                        onSuccessHandler={(file) =>
                          this.getUploadedUrl(file, 'organisationLogo')
                        }
                        onProgressHandler={this.progressHandler}
                      />
                    </div>
                  </div>
                )}

                {/* Hidden FileUpload for when logo exists - similar to editCover */}
                {organisationLogo && (
                  <div
                    ref={this.logoFileUploadRef}
                    className={Style.profileUploader}
                  >
                    <FileUpload
                      isOriginalName
                      name="file"
                      accept={['jpeg', 'png', 'jpg', 'JPEG', 'PNG', 'JPG']}
                      onSuccessHandler={(file) =>
                        this.getUploadedUrl(file, 'organisationLogo')
                      }
                      onProgressHandler={this.progressHandler}
                    />
                  </div>
                )}
              </div>
              <div className="col-md-10 p-0 p-sm-0 p-md-3 p-lg-3">
                <OrgForm userData={userData} />
              </div>
            </div>
          </div>
          <InterestForm
            t={t}
            setInterests={setInterests}
            interests={interests}
            userData={userData}
            tagList={tagList}
            setTagData={setTagData}
          />
          <SocialMediaForm userData={userData} />
          <Privacy userData={userData} />
          <ContactForm userData={userData} />
          <div className="row m-0 justify-content-center mb-auto p-0">
            <Button
              btntype="button"
              id="submitbutton"
              size="large"
              buttonValue={btnValue}
              iconPosition="end"
              clickHandler={() => this.saveData()}
              className="col-md-5 col-12"
              customClass={this.isFormValid() ? '--primaryNavy' : '--disabled'}
            />
          </div>
        </div>
      </>
    );
  }
}

MyAccount.propTypes = {
  t: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
  updateUserProfile: PropTypes.func.isRequired,
  removeProfilePicture: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  setLocation: PropTypes.func.isRequired,
  setInterests: PropTypes.func.isRequired,
  interests: PropTypes.array.isRequired,
  tagList: PropTypes.array.isRequired,
  setTagData: PropTypes.func.isRequired,
  editModeStatus: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => {
  const orgFormValues = getFormValues('organisationForm')(state);
  const interestFormValues = getFormValues('interestForm')(state);
  const socialMediaFormValues = getFormValues('socialMediaForm')(state);
  const privacyFormValues = getFormValues('privacyForm')(state);
  const contactFormValues = getFormValues('contactForm')(state);
  const userProfileFormValues = getFormValues('userProfileForm')(state);
  const isValidOrganisationForm = isValid('organisationForm')(state);
  const isValidInterestForm = isValid('interestForm')(state);
  const isValidSocialMediaForm = isValid('socialMediaForm')(state);
  const isValidPrivacyForm = isValid('privacyForm')(state);
  const isValidContactForm = isValid('contactForm')(state);
  const isValidUserProfileForm = isValid('userProfileForm')(state);

  return {
    orgFormValues,
    interestFormValues,
    socialMediaFormValues,
    privacyFormValues,
    contactFormValues,
    userProfileFormValues,
    isValidUserProfileForm,
    isValidOrganisationForm,
    isValidInterestForm,
    isValidSocialMediaForm,
    isValidPrivacyForm,
    isValidContactForm,
    location: state.user.location,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    setLocation: (payload) => dispatch(setLocation(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation()(MyAccount));
