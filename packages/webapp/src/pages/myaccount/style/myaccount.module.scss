@import '../../../styles/variable.scss';

.pos {
  margin: auto;
  width: 50%;
}

@media (max-width: $breakpoint-md) {
  .pos {
    margin: 10px;
    width: 100%;
  }
}

.inaline {
  display: inline-block;
  margin-right: 20px;
}

.myAccount {
  margin-top: 40px;
  font-size: 28px;
  font-weight: bold;
  line-height: 40px;
  color: $grey-dark;
}

.manageUpdate {
  height: 24px;
  width: 272px;
  color: $grey-medium;
  font-family: Inter;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 24px;
}

.dropdownPos {
  top: 130px !important;
  right: calc(50% - 140px);
  display: none !important;

  &:hover {
    display: block !important;
  }
}

.avatar {
  cursor: pointer;
  border: 2px solid;
}

.avatarContainer {
  width: 130px;
  margin: auto;
  z-index: 99;
}

.avatarContainer:hover + .dropdownPos {
  display: block !important;
}

.textValue {
  color: $grey-medium;
  font-family: 'robotoRegular';
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 24px;
}

.mainContainer {
  margin-bottom: 100px;
}

.smallButton {
  width: 200px;
  left: 25%;
  border: 2px solid $navy;
}

.myAccountHeader {
  color: $black-solid;
  font-family: 'robotoCondensed';
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 0.88px;
  line-height: 40px;
  text-align: center;
}

.background_dashboard {
  background-color: $grey-100;
  min-height: 100vh;
  height: 100%;
  display: block;
  padding-bottom: 30px;
}

.hrBorder {
  box-sizing: border-box;
  height: 1px;
  border: 1px solid $decisionHr;
}

.iAgreeToReceiveE {
  margin-right: 33px;
  margin-left: 33px;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  color: $black;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 24px;
}

// Checkbix style

.container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  margin-top: 24px;
  font-size: 22px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  border: 2px solid $black;
}

.disableMark {
  height: 22px;
  width: 22px;
}

.anchorText {
  color: $magenta;
}

/* mouse over link */
.anchorText:hover {
  color: $magenta;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: $white;
  border-style: solid;
  border-color: $black;
}
.container input:disabled ~ .disableMark {
  border: 2px solid $black;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}
.disableMark:after {
  content: '';
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

.container input:disabled ~ .checkmark:after {
  display: none;
}

.container input:disabled ~ .disableMark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container input:checked ~ .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid $black;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.container input:disabled ~ .disableMark:after {
  top: 12px;
  left: 7px;
  width: 15px;
  height: 5px;
  border-radius: 10%;
  background-color: $grey-400;
  transform: rotate(0deg);
}

.noteButton {
  max-width: 180px;
  margin: 0 auto;
}

.suggestionActive {
  background-color: $myAccountBg;
}
ul.suggestions {
  padding: 0px;
}
.suggestions > li {
  list-style: none;
  display: block;
  text-align: left;
  line-height: 2;
  font-size: 20px;
  padding-left: 5px;
}

.tagsInputClass {
  margin-bottom: 60px;
}

.tagInputFieldClass {
  width: 100%;
  min-height: 38px;
  border: 2px solid $black-solid;
  font-size: 20px;
  padding: 10px;
}

.tagInputFieldClassError {
  width: 100%;
  min-height: 38px;
  border: 2px solid $error-base;
  font-size: 20px;
  padding: 10px;
}

.interestContainer {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 8px;
  border-radius: 20px;
  background-color: $navy;
  color: $white;
  min-height: auto;
  position: relative;
  font-size: 18px;
  margin: 10px;
  display: inline-block;
}

.intrestList {
  padding: 8px;
  border-radius: 20px;
  background-color: $navy;
  color: $white;
  min-height: auto;
  margin: 15px;
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
}

.interestContainer:hover .removeClass {
  opacity: 1;
}

.removeClass {
  width: 26px;
  border: 2px solid #05012d;
  border-radius: 50%;
  height: 0px;
  padding-bottom: 26px;
  background-color: #05012d;
}

.detailsContainer {
  // min-height: 218px;
  border-radius: 6px;
  border: 1px solid #dbdbcf;
  background-color: $theme-white;
  padding: 30px;
}

.suggestionsClass {
  font-size: 20px;
  width: 100%;
  background-color: $white;
  cursor: pointer;
  box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.4);
}

.suggestionsText {
  border-bottom: 1px solid rgba(0, 0, 0, 0.4);
}

.img {
  height: 130px;
  width: 130px;
  border-radius: 50%;
  box-shadow: 1px 1px 2px 0 rgba(0, 0, 0, 0.4);
  margin-right: 25px;
  object-fit: cover;

  @media (max-width: $breakpoint-md) {
    height: 121px;
    width: 120px;
    object-fit: cover;
  }
}

.detailsContainers {
  min-height: 256px;
  border-radius: 8px;
  background-color: $beige;
  margin: 0 auto;
}

.myProfileContainer {
  min-height: 256px;
  background-color: $beige;
  margin: 0 auto;
  padding: 48px;
  border: 1px solid #dbdbcf;
  border-radius: 6px;
}
.editBtn {
  background-color: $navy;
  color: $white;
  font-size: 16px;
  font-weight: bold;
  letter-spacing: 1px;
  line-height: 24px;
  text-align: center;
  padding: 6px 14px;
  font-family: 'chaney';
}

.smallText {
  font-family: 'maisonNeue';
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
  color: #757575;
}

.smallAnchor {
  color: var(--royal, #1743d7);
  text-decoration-line: underline !important;
}

.emailInput {
  color: #05012d !important;
  cursor: not-allowed;
  background-color: #ecece0 !important;
}

.profileImageUploadIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin-top: -30px;
  width: 160px;
  height: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

@media (max-width: 480px) {
  .profileImageUploadIcon {
    width: 100px;
    height: 100px;
    margin-top: -30px;
    top: 50%;
  }

  .profileImageUploadIcon svg {
    width: 32px !important;
    height: 32px !important;
  }
}

.profileUploader {
  position: absolute;
  overflow: hidden;
}
