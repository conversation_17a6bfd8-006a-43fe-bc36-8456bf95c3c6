import React from 'react';
import Button from 'sharedComponents/Button/button';
import Loader from 'sharedComponents/loader';
import PageHeaderSection from 'sharedComponents/PageHeaderSection/pageHeaderSection';
import Submissions from '../submissions/submission';
import CallOutList from 'sharedComponents/Callouts/components/callOutList';
import DiscoverCallOutList from 'sharedComponents/Callouts/components/discovererCalloutList';

const MyCallOutSection = ({
  isLoading,
  showOnlySubmissions,
  handleCreateCallout,
  userData,
  publishedCallOutList,
  callOutList,
  router,
  onViewMore,
  onBack,
  expandSubmissions,
}) => {
  return (
    <div className="pb-5">
      {' '}
      {/* Added padding-bottom */}
      {isLoading ? (
        <Loader />
      ) : (
        <>
          {!showOnlySubmissions && (
            <>
              <PageHeaderSection
                title="My call outs"
                showButton={true}
                buttonLabel="Create a call out"
                onButtonClick={handleCreateCallout}
              />
              <p className="text-center text-primary mb-0 mt-3 p2">
                New opportunities for your projects
              </p>
              <div className="row d-md-none my-3">
                <div className="col-12 text-center">
                  <Button
                    btntype="submit"
                    customClass="waitListBtn"
                    buttonValue="Create a call out"
                    clickHandler={handleCreateCallout}
                  >
                    Create a call out
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* Always show submissions section for testing */}
          <div className="row">
            <Submissions
              onViewMore={onViewMore}
              onBack={onBack}
              expandSubmissions={expandSubmissions}
            />
          </div>

          {!showOnlySubmissions &&
            publishedCallOutList &&
            publishedCallOutList.length > 0 && (
              <div id="myCallouts" className="row mt-32">
                <CallOutList
                  callOutList={publishedCallOutList}
                  userData={userData}
                  source="mycallout"
                />
              </div>
            )}

          {!showOnlySubmissions && callOutList && callOutList.length > 0 && (
            <div id="discoverCallouts">
              <DiscoverCallOutList
                router={router}
                callOutList={callOutList}
                pageType="discovererCallouts"
                callOutContainer="col-12"
                userData={userData}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default MyCallOutSection;
