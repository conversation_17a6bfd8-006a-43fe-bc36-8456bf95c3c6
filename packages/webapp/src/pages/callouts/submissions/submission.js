import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { useSelector } from 'react-redux';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import SubmissionCard from 'sharedComponents/SubmissionCard/submissionCard';
import Button from 'sharedComponents/Button/button';
import Loader from 'sharedComponents/loader';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';

const Submissions = ({ onViewMore, onBack, expandSubmissions }) => {
  const calloutSubmissions = useSelector(
    (state) => state.callout.calloutSubmissions,
  );
  const isLoading = useSelector((state) => state.callout.isLoading);
  const callOutList = useSelector((state) => state.callout.callOutList);
  const myCallOutList = useSelector((state) => state.callout.myCallOutList);

  const [userSubmissions, setUserSubmissions] = useState([]);
  const [shouldShowAllSubmissions, setShouldShowAllSubmissions] = useState(
    expandSubmissions || false,
  );

  // Update shouldShowAllSubmissions when expandSubmissions prop changes
  useEffect(() => {
    if (expandSubmissions && !shouldShowAllSubmissions) {
      setShouldShowAllSubmissions(true);
      // Trigger onViewMore to ensure parent state is in sync, but only if not already expanded
      if (onViewMore) {
        onViewMore();
      }
    }
  }, [expandSubmissions, onViewMore, shouldShowAllSubmissions]);

  // Helper function to find callout data by ID
  const findCalloutData = (calloutId) => {
    const allCallouts = [...(callOutList || []), ...(myCallOutList || [])];
    return allCallouts.find(
      (callout) => callout._id === calloutId || callout.id === calloutId,
    );
  };

  // Helper function to extract callout and organisation data
  const getCalloutInfo = (submission) => {
    let calloutName = 'Untitled Callout';
    let organisationName = '';
    let coverUrl = '';

    // The key insight: the submission's _id IS the callout ID
    // We need to look up the callout data using this ID
    const calloutId = submission._id;

    if (calloutId) {
      const calloutData = findCalloutData(calloutId);

      if (calloutData) {
        calloutName = get(calloutData, 'name', 'Untitled Callout');
        // Use the same path as in callOutList.jsx: 'body.companyName'
        organisationName =
          get(calloutData, 'body.companyName', '') ||
          get(calloutData, 'companyName', '') ||
          get(calloutData, 'company', '');
      }
    }

    // Get coverUrl from submission snapshot data
    coverUrl =
      get(submission, 'submissions.snapshot.body.coverPic') ||
      get(submission, 'submissions.snapshot.body.logo') ||
      get(submission, 'snapshot.body.coverPic') ||
      get(submission, 'snapshot.body.logo') ||
      '';

    return { calloutName, organisationName, coverUrl };
  };

  // Process submissions from the API
  useEffect(() => {
    // Handle different response formats
    let submissionsArray = calloutSubmissions;

    // Check if we have a paginated response
    if (
      calloutSubmissions &&
      calloutSubmissions.docs &&
      Array.isArray(calloutSubmissions.docs)
    ) {
      submissionsArray = calloutSubmissions.docs;
    }

    if (!Array.isArray(submissionsArray)) {
      setUserSubmissions([]);
      return;
    }

    if (submissionsArray.length === 0) {
      setUserSubmissions([]);
      return;
    }

    try {
      // Map the submissions to the format expected by the UI
      const formattedSubmissions = submissionsArray
        .filter((submission) => {
          // More flexible validation to handle different response formats
          const isValid =
            submission &&
            // Direct submission properties
            (submission._id ||
              // Nested submission properties
              submission.callout ||
              submission.slate ||
              submission.snapshot ||
              // Submissions array inside the response
              (submission.submissions && submission.submissions.length > 0));

          return isValid;
        })
        .flatMap((submission) => {
          // Handle case where the API returns an object with a submissions array
          if (
            submission.submissions &&
            Array.isArray(submission.submissions) &&
            submission.submissions.length > 0
          ) {
            // This case doesn't apply based on the debug data, but keeping for compatibility
            return submission.submissions.map((subItem) => {
              const { calloutName, organisationName, coverUrl } =
                getCalloutInfo(submission);

              return {
                ...subItem,
                _id: subItem._id || submission._id,
                calloutName,
                calloutId: submission._id,
                organisationName,
                companyName: organisationName,
                coverUrl,
                type: subItem.submissionType || 'submission',
                slate: subItem.slate || {},
                snapshot: subItem.snapshot || {},
                status: subItem.status || 'pending',
                createdAt: subItem.createdAt || submission.createdAt,
                updatedAt: subItem.updatedAt || submission.updatedAt,
                creator: subItem.creator || submission.creator || {},
                project: subItem.project || submission.project || {},
              };
            });
          }

          // Handle case where the API returns a project object
          if (submission.project) {
            const { calloutName, organisationName, coverUrl } =
              getCalloutInfo(submission);

            return [
              {
                ...submission,
                _id: submission._id || get(submission, 'project._id', ''),
                calloutName,
                calloutId: submission._id,
                organisationName,
                companyName: organisationName,
                coverUrl,
                type:
                  submission.submissionType ||
                  (submission.slate
                    ? 'slate'
                    : submission.snapshot
                      ? 'submission'
                      : 'project'),
                slate: submission.slate || {},
                snapshot: submission.snapshot || {},
                status: submission.status || 'pending',
                createdAt:
                  submission.createdAt ||
                  get(submission, 'project.createdAt', ''),
                updatedAt:
                  submission.updatedAt ||
                  get(submission, 'project.updatedAt', ''),
                creator:
                  submission.creator ||
                  get(submission, 'project.creator', {}) ||
                  get(submission, 'user', {}),
                project: submission.project,
              },
            ];
          }

          // Handle direct submission objects (this is the main case based on debug data)
          const { calloutName, organisationName, coverUrl } =
            getCalloutInfo(submission);

          // For submissions with nested submission data (like the debug data shows)
          const actualSubmission = submission.submissions || submission;

          return [
            {
              ...submission,
              _id: get(actualSubmission, '_id', submission._id),
              calloutName,
              calloutId: submission._id, // The top-level _id is the callout ID
              organisationName,
              companyName: organisationName,
              coverUrl,
              type:
                submission.type ||
                get(actualSubmission, 'submissionType') ||
                (get(actualSubmission, 'slate')
                  ? 'slate'
                  : get(actualSubmission, 'snapshot')
                    ? 'submission'
                    : 'unknown'),
              slate: get(actualSubmission, 'slate', {}),
              snapshot: get(actualSubmission, 'snapshot', {}),
              status: get(actualSubmission, 'status', 'pending'),
              createdAt:
                get(actualSubmission, 'createdAt') ||
                get(actualSubmission, 'submissionAddedAt') ||
                submission.createdAt,
              updatedAt:
                get(actualSubmission, 'updatedAt') || submission.updatedAt,
              creator:
                get(actualSubmission, 'creator') || submission.creator || {},
              project: get(actualSubmission, 'project', {}),
              // Add the actual submission data
              submissionData: actualSubmission,
            },
          ];
        });

      setUserSubmissions(formattedSubmissions);
    } catch (error) {
      console.error('Error processing submissions:', error);
      setUserSubmissions([]);
    }
  }, [calloutSubmissions, callOutList, myCallOutList]);

  // Make sure userSubmissions is an array
  const safeUserSubmissions = Array.isArray(userSubmissions)
    ? userSubmissions
    : [];

  // Show all submissions in the carousel
  const visibleSubmissions = safeUserSubmissions;

  // Check if there are additional submissions to show in detailed view
  const hasAdditionalSubmissions = safeUserSubmissions.length > 3;

  const handleViewMoreSubmissions = () => {
    setShouldShowAllSubmissions(true);
    if (onViewMore) onViewMore();
  };

  const handleNavigateBack = () => {
    setShouldShowAllSubmissions(false);
    if (onBack) onBack();
  };

  return (
    <div className="col-12 mt-0 mt-md-32 mt-lg-32">
      <SectionHeader
        title="MY CALL OUT SUBMISSIONS"
        showButton={
          !shouldShowAllSubmissions && hasAdditionalSubmissions && !isLoading
        }
        buttonText="View All"
        customClass="viewListBtn"
        alignment={shouldShowAllSubmissions ? 'center' : 'left'}
        showBackButton={shouldShowAllSubmissions}
        handleBackBtn={handleNavigateBack}
        onButtonClick={handleViewMoreSubmissions}
        headerTitleClass="fs-20"
      />

      {isLoading ? (
        <div className="text-center py-4">
          <Loader />
          <p className="mt-3">Loading all submissions...</p>
        </div>
      ) : (
        <>
          {visibleSubmissions.length > 0 ? (
            <div className="pt-3 pb-5 border-bottom">
              {!shouldShowAllSubmissions ? (
                <CustomCarousel
                  items={visibleSubmissions.slice(0, 9)}
                  renderItem={(submissionItem) => (
                    <SubmissionCard
                      submission={submissionItem}
                      key={
                        submissionItem._id ||
                        Math.random().toString(36).substring(2, 9)
                      }
                    />
                  )}
                  slidesPerPage={3}
                  autoplay={false}
                />
              ) : (
                <div className="d-flex flex-wrap" style={{ gap: '16px' }}>
                  {visibleSubmissions.map((submissionItem, index) => (
                    <div
                      key={submissionItem._id || index}
                      className="submission-card-wrapper"
                      style={{
                        flex: '0 0 100%',
                        maxWidth: '100%',
                        marginBottom: '16px',
                      }}
                    >
                      <style jsx>{`
                        @media (min-width: 768px) {
                          .submission-card-wrapper {
                            flex: 0 0 calc(33.333% - 11px) !important;
                            max-width: calc(33.333% - 11px) !important;
                            margin-bottom: 0 !important;
                          }
                        }
                      `}</style>
                      <SubmissionCard submission={submissionItem} />
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-4 w-100 border-bottom">
              <p className="mb-0">No submissions found.</p>
            </div>
          )}

          {!shouldShowAllSubmissions && hasAdditionalSubmissions && (
            <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom">
              <Button
                btntype="button"
                customClass="viewListBtn"
                className="w-100 py-2 px-3"
                buttonValue="View All"
                clickHandler={handleViewMoreSubmissions}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Submissions;
