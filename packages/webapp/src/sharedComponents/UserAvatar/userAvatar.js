// src/components/UserAvatar/UserAvatar.jsx
import React from 'react';
import styles from './userAvatar.module.scss';

/**
 * UserAvatar Component
 * Displays a user avatar with initials as fallback
 */
const UserAvatar = ({ src, alt, size = 'md' }) => {
  // Extract initials from alt text as fallback
  const initials = alt
    ?.split(' ')
    .map((name) => name[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);

  return (
    <div className={`${styles.avatar} ${styles[size]}`}>
      {src ? (
        <img src={src} alt={alt || 'User avatar'} className={styles.image} />
      ) : (
        <div className={styles.initials} aria-label={alt}>
          {initials || 'U'}
        </div>
      )}
    </div>
  );
};

export default UserAvatar;
