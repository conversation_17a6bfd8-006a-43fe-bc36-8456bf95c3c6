/* src/components/UserAvatar/UserAvatar.module.css */

.avatar {
  border-radius: 50%;
  overflow: hidden;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent avatar from resizing */
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.initials {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-weight: 500;
  color: #333;
}

/* Size variations */
.sm {
  width: 28px;
  height: 28px;
  font-size: 0.75rem;
}

.md {
  width: 46px;
  height: 46px;
}

.lg {
  width: 48px;
  height: 48px;
  font-size: 1rem;
}
