import React from 'react';
import SmashLogo from 'sharedComponents/SmashLogo/smashLogo';
import styles from './pricingcard.module.scss';
import { get } from 'lodash';
import { useSelector } from 'react-redux';

function PlanDetails({ priceText, promoCodeDetails }) {
  const billingCycle = useSelector((state) => state.subscription.billingCycle);
  return (
    <>
      <SmashLogo
        src={`/assets/svg/smashLogo.svg`}
        height="51px"
        width="168px"
        variant={'pro'}
      />
      <div
        className="flex"
        style={{
          marginTop: '24px',
        }}
      >
        <p
          className={`${styles.priceAmount}`}
          style={{ alignItems: 'flex-start', display: 'flex' }}
        >
          <span
            className={styles.priceAmount}
            style={{
              fontSize: '20px',
              lineHeight: '22px',
            }}
          >
            {priceText}
          </span>
          <span className={`${styles.currentPlan} ml-1 align-self-end`}>
            {billingCycle === 'monthly' ? '/ month' : '/ year'}
          </span>
          {promoCodeDetails && (
            <span className={`${styles.discountedValue} ml-1 align-self-end`}>
              {get(promoCodeDetails, 'type', null) === 'percent_off'
                ? `${get(promoCodeDetails, 'amount', null)}% OFF`
                : `£${get(promoCodeDetails, 'amount', null)} OFF`}
            </span>
          )}
        </p>
      </div>
    </>
  );
}

export default PlanDetails;
