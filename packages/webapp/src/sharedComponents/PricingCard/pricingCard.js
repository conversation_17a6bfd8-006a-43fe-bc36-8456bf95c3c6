import React from 'react';
import { useSelector } from 'react-redux';
import { get } from 'lodash';
import Button from 'sharedComponents/CascadeTemplate/Common/Button/button';
import SmashLogo from 'sharedComponents/SmashLogo/smashLogo';
import styles from './pricingcard.module.scss';

const PricingCard = ({
  priceText,
  duration,
  features,
  buttonType = 'secondory-button', // Update to match the SCSS class name
  buttonText,
  buttonLink,
  plan,
  screen,
  borderColor = '#CDCCD5',
  backgroundColor = 'var(--light, #FFF)',
  description,
  descDuration,
  promoCodeDetails,
  paymentScreen,
}) => {
  const subscriptionJourney = useSelector(
    (state) => state.subscription.subscriptionJourney,
  );
  const feature = get(subscriptionJourney, 'feature');
  return (
    <div
      className={`${styles.pricingCard} ${paymentScreen ? 'p-0' : ''}`} // Apply the SCSS module class
      style={{
        border:
          screen === 'paymentScreen' ? 'none' : `1px solid ${borderColor}`,
        background: backgroundColor,
      }}
    >
      <SmashLogo
        src={`/assets/svg/smashLogo.svg`}
        height="51px"
        width="168px"
        variant={plan}
      />
      <div
        className="flex"
        style={{
          // marginTop: '24px',
          marginTop:
            screen === 'onboarding' && plan === 'enterprise' ? '13px' : '24px',
          marginBottom:
            screen === 'onboarding' && plan === 'enterprise' ? '40px' : '0px',
        }}
      >
        {/* {screen === 'onboarding' && plan === 'pro' && (
          <p className={`${styles.trialText} mb-0`}>{trialText}</p>
        )} */}
        {/* <p className={`${styles.currentPlan} mb-2 mt-12`}>
          {screen === 'onboarding' && plan === 'pro'
            ? 'No commitment or card required'
            : ''}
        </p> */}
        <p
          className={`${styles.priceAmount} mb-0`}
          style={{
            alignItems: 'flex-start',
            display: 'flex',
          }}
        >
          {/* <span className="mr-2">
            {' '}
            {screen === 'onboarding' && plan === 'pro' ? 'then  ' : ''}
          </span> */}
          <span
            className={styles.priceAmount}
            style={{
              fontSize: '20px',
              lineHeight: '22px',
              visibility: plan === 'enterprise' ? 'hidden' : 'visible',
            }}
          >
            {priceText}
          </span>
          <span
            className={`${styles.currentPlan} ml-1 align-self-center`}
            style={{ visibility: plan === 'enterprise' ? 'hidden' : 'visible' }}
          >
            {duration}
          </span>

          {promoCodeDetails &&
            typeof promoCodeDetails === 'object' &&
            !Array.isArray(promoCodeDetails) &&
            (get(promoCodeDetails, 'type', null) || get(promoCodeDetails, 'amount', null)) && (
              <span
                className={`${styles.discountedValue} ml-1 align-self-center`}
              >
                {get(promoCodeDetails, 'type', null) === 'percent_off'
                  ? `${get(promoCodeDetails, 'amount', null)}% OFF`
                  : `£${get(promoCodeDetails, 'amount', null)} OFF`}
              </span>
            )}
        </p>
      </div>

      {screen !== 'paymentScreen' &&
        feature !== 'learnSmashPro' &&
        (buttonText ? (
          <Button
            customClass={buttonType}
            btntype="submit"
            id="submitbutton"
            buttonValue={buttonText}
            clickHandler={() => buttonLink(plan)}
            className="save-btn w-100 mt-4"
          />
        ) : (
          <div
            className="mt-24"
            style={{ height: '40px', alignItems: 'center' }}
          >
            <p className={`${styles.currentPlan} pt-2`}>{'Current Plan'}</p>
          </div>
        ))}

      {screen === 'onboarding' && (
        <div
          className={`${plan === 'pro' ? 'mb-0 mt-3' : 'mt-4'
            } d-flex justify-content-between`}
        >
          {plan === 'pro' && <p className={styles.currentPlan}>then</p>}
          <p className={`${styles.priceAmount} ml-2`}>{description}</p>
          <p
            className={`${styles.currentPlan} ${plan === 'pro' ? 'ml-1' : ''}`}
          >
            {descDuration}
          </p>
        </div>
      )}

      <div
        style={{
          width: '100%',
          height: '0.0625rem',
          background: borderColor,
          // marginTop: '1.5rem', // Add spacing after the button
          marginTop:
            screen === 'onboarding'
              ? plan === 'pro'
                ? '8px'
                : plan === 'enterprise'
                  ? '22px'
                  : '24px'
              : '24px',

          marginBottom: '1.5rem',
        }}
      />

      {features.map((feature, index) => (
        <div
          style={{
            gap: '12px',
            marginBottom: '0.75rem',
            display: 'flex',
            alignItems: 'baseline',
          }}
          key={index}
        >
          <div>
            {screen !== 'paymentScreen' ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1.25rem" // 20px = 1.25rem
                height="1.3125rem" // 21px = 1.3125rem
                viewBox="0 0 20 21"
                fill="none"
              >
                <path
                  d="M10 2.61499C5.40835 2.61499 1.66669 6.35666 1.66669 10.9483C1.66669 15.54 5.40835 19.2817 10 19.2817C14.5917 19.2817 18.3334 15.54 18.3334 10.9483C18.3334 6.35666 14.5917 2.61499 10 2.61499ZM13.9834 9.03166L11.6209 11.3942L9.25835 13.7567C9.14169 13.8733 8.98335 13.94 8.81669 13.94C8.65002 13.94 8.49169 13.8733 8.37502 13.7567L6.01669 11.3983C5.77502 11.1567 5.77502 10.7567 6.01669 10.515C6.25835 10.2733 6.65835 10.2733 6.90002 10.515L8.81669 12.4317L13.1 8.14832C13.3417 7.90666 13.7417 7.90666 13.9834 8.14832C14.225 8.38999 14.225 8.78166 13.9834 9.03166Z"
                  fill="#05012D"
                />
                <path
                  d="M13.9834 9.03166L11.6209 11.3942L9.25835 13.7567C9.14169 13.8733 8.98335 13.94 8.81669 13.94C8.65002 13.94 8.49169 13.8733 8.37502 13.7567L6.01669 11.3983C5.77502 11.1567 5.77502 10.7567 6.01669 10.515C6.25835 10.2733 6.65835 10.2733 6.90002 10.515L8.81669 12.4317L13.1 8.14832C13.3417 7.90666 13.7417 7.90666 13.9834 8.14832C14.225 8.38999 14.225 8.78166 13.9834 9.03166Z"
                  fill="white"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
              >
                <path
                  d="M9.00002 0.739075C4.40835 0.739075 0.666687 4.48074 0.666687 9.07241C0.666687 13.6641 4.40835 17.4057 9.00002 17.4057C13.5917 17.4057 17.3334 13.6641 17.3334 9.07241C17.3334 4.48074 13.5917 0.739075 9.00002 0.739075ZM12.9834 7.15574L8.25835 11.8807C8.14169 11.9974 7.98335 12.0641 7.81669 12.0641C7.65002 12.0641 7.49169 11.9974 7.37502 11.8807L5.01669 9.52241C4.77502 9.28074 4.77502 8.88074 5.01669 8.63907C5.25835 8.39741 5.65835 8.39741 5.90002 8.63907L7.81669 10.5557L12.1 6.27241C12.3417 6.03074 12.7417 6.03074 12.9834 6.27241C13.225 6.51407 13.225 6.90574 12.9834 7.15574Z"
                  fill="#00E5D5"
                />
                <path
                  d="M12.9834 7.15574L8.25835 11.8807C8.14169 11.9974 7.98335 12.0641 7.81669 12.0641C7.65002 12.0641 7.49169 11.9974 7.37502 11.8807L5.01669 9.52241C4.77502 9.28074 4.77502 8.88074 5.01669 8.63907C5.25835 8.39741 5.65835 8.39741 5.90002 8.63907L7.81669 10.5557L12.1 6.27241C12.3417 6.03074 12.7417 6.03074 12.9834 6.27241C13.225 6.51407 13.225 6.90574 12.9834 7.15574Z"
                  fill="#05012D"
                />
              </svg>
            )}
          </div>
          <div className={`${styles.features}`}>{feature.name}</div>
        </div>
      ))}
    </div>
  );
};

export default PricingCard;
