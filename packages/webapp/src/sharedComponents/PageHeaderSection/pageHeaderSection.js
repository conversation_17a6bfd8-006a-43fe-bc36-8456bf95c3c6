import React from 'react';
import InlineSvg from 'sharedComponents/inline-svg';
import Button from 'sharedComponents/Button/button';

const PageHeaderSection = ({
  title,
  showButton = false,
  buttonLabel = '',
  onButtonClick,
  showBackButton = false,
  backHref = '/',
  titleClass = '',
}) => {
  return (
    <div className="row align-items-center mt-5 mt-md-0 mt-lg-0">
      {/* Back Button - Modified for better small screen alignment */}
      <div className="col-2 col-md-4 text-start d-flex align-items-center">
        {showBackButton && (
          <span
            role="button"
            className="cursor-pointer"
            onClick={() => {
              window.location.href = backHref;
            }}
          >
            <InlineSvg height={24} width={24} src="/assets/svg/backArrow.svg" />
          </span>
        )}
      </div>

      {/* Title */}
      <div className={`col-8 col-md-4 text-center ${titleClass}`}>
        <h1 className="text-primary mb-0">{title}</h1>
      </div>

      {/* Button */}
      <div className="col-2 col-md-4 text-right d-none d-md-block">
        {showButton && (
          <Button
            btntype="submit"
            customClass="waitListBtn"
            buttonValue={buttonLabel}
            clickHandler={onButtonClick}
          >
            {buttonLabel}
          </Button>
        )}
      </div>
    </div>
  );
};

export default PageHeaderSection;
