/* eslint-disable @next/next/no-img-element */
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import Icon from 'sharedComponents/Icon/Icon';
import InlineSvg from 'sharedComponents/inline-svg';
import { ProgressBar } from 'react-bootstrap';

// This component is used for upload file.
const FileInputField = ({ input }) => {
  const [progress, setProgress] = useState(0);
  const [isProgress, setIsProgress] = useState(false);
  const [preview, setPreview] = useState(null);
  const [style, setStyle] = useState('');

  // Get uploaded video urls.
  const getUploadedUrl = (uploadedFile) => {
    setIsProgress(false);
    setStyle('');
    input.onChange(uploadedFile.file);
  };

  // Set progress bar handler.
  const onProgressHandler = (percentage) => {
    setProgress(percentage);
  };

  // This method call when file is uploaded.
  const onPreprocessHandler = (file) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onloadend = () => {
      setPreview(reader.result);
      setStyle('--preview');
      setIsProgress(true);
    };
  };

  let imageUrl = input.value;
  if (preview) {
    imageUrl = preview;
  }

  return (
    <>
      <FileUpload
        accept={['jpg', 'png', 'jpeg', 'JPEG', 'JPG', 'PNG', 'svg']}
        onSuccessHandler={getUploadedUrl}
        onProgressHandler={onProgressHandler}
        onPreprocessHandler={onPreprocessHandler}
        cropShape="rect"
      />
      {imageUrl ? (
        <>
          <div
            className={`imagePreviewArea ${style}`}
            style={{ position: 'relative' }}
          >
            <img data-cy="imageCreativeTeam" src={imageUrl} alt="preview" />
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none',
              }}
            >
              <InlineSvg
                width="60px"
                height="60px"
                src="/assets/svg/upload-Icon-2.svg"
                alt="pc"
              />
            </div>
          </div>
          {isProgress && (
            <ProgressBar
              now={progress}
              label={`${progress}%`}
              className="progressBar"
            />
          )}
        </>
      ) : (
        <div className="imgUpload --circle">
          <Icon iconSize="48px" color="#414141" icon={UploadButtonSvgPath} />
        </div>
      )}
    </>
  );
};

FileInputField.propTypes = {
  input: PropTypes.object.isRequired,
};

export default FileInputField;
