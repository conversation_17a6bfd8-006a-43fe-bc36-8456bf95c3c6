import React, { useState, useCallback, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import { withTranslation } from 'react-i18next';
import { get } from 'lodash';
import InlineSvg from 'react-inlinesvg';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import GettyImages from 'sharedComponents/GettyAndUppy/gettyImages';
import { createGettySignIn } from 'reducer/project';

const Index = (props) => {
  const [imageUrlList, setImageUrlList] = useState([]);
  const [coverImageUploadStatus, setCoverImageUploadStatus] = useState(false);
  const fileUploadRef = useRef(null);

  const addRemoveImage = useCallback(
    (item, action) => {
      const uri = get(item, 'display_sizes[0].uri');
      if (action === 'remove') {
        setImageUrlList((prevList) =>
          prevList.filter((i) => i.uploadURL !== uri),
        );
      } else {
        setImageUrlList((prevList) => [
          ...prevList,
          {
            uploadURL: uri,
            source: 'getty',
            meta: { name: 'Getty', caption: 'Getty Image' },
          },
        ]);
        props.onImageUpload(uri);
      }
    },
    [props],
  );

  const {
    t,
    className,
    isGettyAuthorized,
    uploadText,
    browseFilesText,
    gettyText,
    uploadImageHandler,
    createGettySignIn,
    projectPreviewData,
    showSections,
    gettyStatus,
    userData,
    setModal,
  } = props;

  const sectionSize =
    showSections === 'gettyUppy' ? 'col-12 col-md-6' : 'col-12';

  const handleIconClick = () => {
    // Trigger the file input directly
    if (fileUploadRef.current) {
      const fileInput = fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  // Style the file input to be invisible
  useEffect(() => {
    if (fileUploadRef.current) {
      const fileInput = fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.top = '-9999px';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
      }
    }
  }, []);

  return (
    <>
      <div className="upload-container container bg-secondary p-5 mt-5">
        <div className="row">
          {showSections === 'uploadImg' || showSections === 'gettyUppy' ? (
            <div className={`${sectionSize} text-center mb-4`}>
              <div className="d-flex flex-column align-items-center justify-content-center">
                <h3>{uploadText}</h3>
                <div className="browse-files d-flex align-items-center justify-content-center flex-column mt-3">
                  {coverImageUploadStatus ? (
                    <div className="d-flex">
                      {/* FileUpload now direct, so nothing here */}
                    </div>
                  ) : (
                    <div className="d-flex flex-column align-items-center">
                      <InlineSvg
                        src="/assets/svg/imageUploader.svg"
                        onClick={handleIconClick}
                        className="upload-icon"
                        style={{ cursor: 'pointer' }}
                      />
                      <p className="mt-3">{browseFilesText}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : null}

          {showSections === 'getty' || showSections === 'gettyUppy' ? (
            <div className={`${sectionSize} text-center`}>
              <h3>{gettyText}</h3>
              <div className="mt-5">
                <GettyImages
                  t={t}
                  isGettyAuthorized={isGettyAuthorized}
                  projectPreviewData={projectPreviewData}
                  addRemoveImage={addRemoveImage}
                  className={className}
                  setModal={setModal}
                  createGettySignIn={createGettySignIn}
                  uploadImageHandler={uploadImageHandler}
                  gettyStatus={gettyStatus}
                  imageUrlList={imageUrlList}
                  userData={userData}
                />
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* FileUpload component visible but file input styled to be invisible */}
      <div 
        ref={fileUploadRef}
        data-cy="uploadImageArtwork"
        style={{ 
          position: 'relative',
          width: '1px',
          height: '1px',
          overflow: 'hidden'
        }} // Keep component in DOM but minimal size
      >
        <FileUpload
          name="coverImage"
          accept={['jpeg', 'jpg', 'png']}
          onSuccessHandler={(result) => {
            const url = result.coverImage?.url || result.coverImage;
            setImageUrlList((prev) => [
              ...prev,
              { uploadURL: url, source: 'fileupload' },
            ]);
            props.onImageUpload(url); // Callback to parent
          }}
          onProgressHandler={() => setCoverImageUploadStatus(true)}
          onPreprocessHandler={() => setCoverImageUploadStatus(true)}
          enableCropping={true}
          cropShape="rect"
          cropHeight={320}
          className="uploadStyleBtn"
        />
      </div>
    </>
  );
};

Index.defaultProps = {
  className: '',
  modalSize: 'lg',
  uploadText: 'Upload files',
  browseFilesText: 'Browse files here',
  gettyText: 'Use free Getty Images content',
  showSections: 'gettyUppy',
};

Index.propTypes = {
  uploadText: PropTypes.string,
  browseFilesText: PropTypes.string,
  gettyText: PropTypes.string,
  t: PropTypes.func.isRequired,
  isGettyAuthorized: PropTypes.bool.isRequired,
  uploadImageHandler: PropTypes.func.isRequired,
  projectPreviewData: PropTypes.object.isRequired,
  createGettySignIn: PropTypes.func.isRequired,
  showSections: PropTypes.oneOf(['uploadImg', 'getty', 'gettyUppy']),
  onImageUpload: PropTypes.func.isRequired, // Callback for parent component
};

const mapStateToProps = (state) => {
  return {
    gettyStatus: state.project.gettyStatus,
  };
};

const mapDispatchToProps = (dispatch) => {
  return {
    createGettySignIn: (data) => dispatch(createGettySignIn(data)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withTranslation('common')(Index)));
