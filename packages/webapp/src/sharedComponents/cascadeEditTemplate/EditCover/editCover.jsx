import React, { useState, useRef, useEffect } from 'react';
import { Field, reduxForm } from 'redux-form';
import { withTranslation } from 'react-i18next';
import InlineSvg from 'react-inlinesvg';
import { required } from 'validation/commonValidation';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import RenderField from 'sharedComponents/CascadeTemplate/Common/CascadeRenderField/renderField';
import EditHeader from '../CommonEdit/editHeader';

const EditCover = ({
  t,
  handleSubmit,
  handleCancel,
  setCoverImageUploadStatus,
  coverImgUploadStatus,
  setCoverImage,
  coverImage,
  handleCoverSubmit,
}) => {
  const [previewImage, setPreviewImage] = useState(coverImage);
  const fileUploadRef = useRef(null);

  const fields = [
    { name: 'producer', label: 'Produced by' },
    { name: 'director', label: 'Directed by' },
    { name: 'writer', label: 'Written by' },
  ];

  const handleIconClick = () => {
    // Trigger the file input directly
    if (fileUploadRef.current) {
      const fileInput = fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  // Style the file input to be invisible
  useEffect(() => {
    if (fileUploadRef.current) {
      const fileInput = fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.top = '-9999px';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
      }
    }
  }, []);

  return (
    <>
      <div>
        <div className="d-flex flex-column align-items-center">
          {coverImgUploadStatus ? null : (
            <>
              {previewImage ? (
                <div
                  className="position-relative"
                  style={{ width: '100%', height: '320px' }}
                >
                  <img
                    src={previewImage}
                    alt={t('coverImageAlt')}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              ) : null}
              <InlineSvg
                src={`${previewImage ? '/assets/svg/upload-Icon-2.svg' : '/assets/svg/imageUploader.svg'}`}
                onClick={handleIconClick} // Trigger file upload directly
                className={`${previewImage ? 'position-absolute m-100' : ''} mt-5`}
                style={{ cursor: 'pointer' }}
              />
            </>
          )}
        </div>
      </div>
      
      {/* FileUpload component visible but file input styled to be invisible */}
      <div 
        ref={fileUploadRef}
        data-cy="uploadImageArtwork"
        style={{ 
          position: 'relative',
          width: '1px',
          height: '1px',
          overflow: 'hidden'
        }} // Keep component in DOM but minimal size
      >
        <FileUpload
          name="coverImage"
          accept={['jpeg', 'jpg', 'png']}
          onSuccessHandler={(result) => {
            // result is { coverImage: url } or { coverImage: { url, ... } }
            const url = result.coverImage?.url || result.coverImage;
            setPreviewImage(url);
            setCoverImage(url);
            setCoverImageUploadStatus(false);
          }}
          onProgressHandler={() => setCoverImageUploadStatus(true)}
          onPreprocessHandler={() => setCoverImageUploadStatus(true)}
          enableCropping={true}
          cropShape="rect"
          cropHeight={320}
          className="uploadStyleBtn"
        >
          {/* Optionally, you can add children here for custom button UI */}
        </FileUpload>
      </div>
      
      <div className="section cover-section">
        <div className="container block">
          <form onSubmit={handleSubmit(handleCoverSubmit)}>
            <EditHeader
              id="coverSave"
              title="Cover"
              handleCancel={handleCancel}
            />
            <div className="form-section mt-5">
              <header>
                <h3 className="mb-12">Title</h3>
              </header>
              <Field
                component={RenderField}
                name="title"
                placeholder="title"
                type="text"
                className="input-field bg-dark"
                validate={[required]}
              />
              <section
                className="row justify-content-between mt-36"
                style={{ margin: '0px -12px' }}
              >
                {fields.map((field) => (
                  <article
                    key={field.name}
                    className="col-12 col-md-4 col-lg-4"
                    style={{ padding: '12px' }}
                  >
                    <h3 className="mb-12">{field.label}</h3>
                    <Field
                      component={RenderField}
                      name={field.name}
                      placeholder={t(field.name)}
                      type="text"
                      className="input-field"
                    />
                  </article>
                ))}
              </section>
            </div>
          </form>
        </div>
      </div>
    </>
  );
};

export default reduxForm({
  form: 'editCoverForm',
  enableReinitialize: true,
})(withTranslation('common')(EditCover));
