import React, { PureComponent } from 'react';
import { withRouter } from 'next/router';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import store from 'store';
import Uppy from '@uppy/core';
import AwsS3 from '@uppy/aws-s3';
import ImageEditor from '@uppy/image-editor';
import { Dashboard } from '@uppy/react';
import '@uppy/core/dist/style.css';
import '@uppy/dashboard/dist/style.css';
import '@uppy/image-editor/dist/style.css';

class UppyDashboard extends PureComponent {
  constructor(props) {
    super(props);

    this.uppy = new Uppy({
      autoProceed: false,
      debug: true,
      restrictions: {
        maxFileSize: Number(process.env.MaxSizeArtwork),
        maxNumberOfFiles: 1,
        minNumberOfFiles: 1,
        allowedFileTypes: ['image/png', 'image/jpeg', 'image/jpg'],
      },
      locale: {
        strings: {
          exceedsSize:
            'Failed to add %{file} %{size} as it exceeded the file size limit of 4mb.',
          save: 'APPLY',
          cancel: 'X',
        },
      },
    });

    this.uppy.use(ImageEditor, {
      quality: 0.8,
      cropperOptions: {
        viewMode: 1,
        background: false,
        autoCropArea: 0.8,
        responsive: true,
      },
      actions: {
        revert: false,
        rotate: false,
        granularRotate: false,
        flip: false,
        zoomIn: true,
        zoomOut: true,
        cropSquare: true,
        cropWidescreen: true,
        cropWidescreenVertical: true,
      },
    });

    this.uppy.use(AwsS3, {
      getUploadParameters: (file) => {
        const token = store.get('persist:auth')?.token;
        if (!token) return false;

        return fetch(
          `${process.env.SmashApiBaseUrl}/v1/upload/generatePresignedUrl`,
          {
            method: 'post',
            headers: {
              accept: 'application/json',
              'content-type': 'application/json',
              Authorization: `Bearer ${JSON.parse(token)}`,
            },
            body: JSON.stringify({ filename: file.name }),
          },
        )
          .then((response) => response.json())
          .then((data) => ({
            method: 'PUT',
            url: data.data.signedUrl,
            fields: [],
            headers: {
              'Content-Type': file.type,
              'Content-Disposition': `attachment; filename=${encodeURIComponent(file.name)}`,
            },
          }));
      },
    });

    // ⛔ Prevent redirection to Dashboard after edit
    this.uppy.on('file-editor:complete', () => {
      // ✅ Trigger upload directly
      this.uppy.upload();
    });

    // Handle cancel from built-in Image Editor Cancel button
    this.uppy.on('file-editor:cancel', () => {
      this.uppy.cancelAll();
      if (this.props.setCoverImageUploadStatus)
        this.props.setCoverImageUploadStatus(false);
    });

    this.uppy.on('complete', (result) => {
      const { successful } = result;
      const { setCoverImage, setCoverImageUploadStatus } = this.props;
      if (successful.length > 0) {
        setCoverImage(successful[0].uploadURL);
        setCoverImageUploadStatus(false);
      }
    });
  }

  componentWillUnmount() {
    this.uppy.close();
  }

  render() {
    const { uppyId } = this.props;

    return (
      <div id="edit" style={{ zIndex: '100', position: 'relative' }}>
        <Dashboard
          theme="dark"
          plugins={['ImageEditor']}
          id={uppyId}
          uppy={this.uppy}
          hideUploadButton={true}
          showProgressDetails={true}
          proudlyDisplayPoweredByUppy={false}
          note="Only JPEG/PNG files up to 4MB"
          autoOpenFileEditor={true}
        />

        {/* Hide Save/Cancel inside Image Editor */}
        <style jsx global>{`
          .uppy-Editor-footer {
            display: none !important;
          }
          .uppy-DashboardContent {
            display: none !important;
          }
        `}</style>
      </div>
    );
  }
}

UppyDashboard.defaultProps = {
  className: '',
  modalSize: 'lg',
  uppyId: 'uppyId',
};

UppyDashboard.propTypes = {
  uppyId: PropTypes.string,
  setCoverImage: PropTypes.func.isRequired,
  setCoverImageUploadStatus: PropTypes.func.isRequired,
};

export default withRouter(withTranslation('common')(UppyDashboard));
