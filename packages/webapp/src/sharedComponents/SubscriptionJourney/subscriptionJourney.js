import React, { useEffect, useState } from 'react';
import AiModal from 'sharedComponents/AiModal/AiModal';
import ShowPlans from 'sharedComponents/Plans/showPlans';
import PaymentScreen from 'sharedComponents/PaymentScreen/paymentScreen';
import {
  get,
  // isEmpty
} from 'lodash';
import { useRouter } from 'next/navigation';
import {
  getClientSecretKey,
  getSubscription,
  setBillingCycle,
  setSubscriptionJourneyModal,
} from 'reducer/subscription';
import { useDispatch, useSelector } from 'react-redux';
import SmashSubscriptionWelcome from 'sharedComponents/SmashSubscriptionWelcome/smashSubscriptionWelcome';
import features from './features.json';

function SubscriptionJourney() {
  const [currentScreen, setCurrentScreen] = useState('showPlans');
  const [currentPlan, setCurrentPlan] = useState('');
  const [featureDetails, setFeatureDetails] = useState('');
  const [selectedPlanDetalils, setSelectedPlanDetails] = useState('');
  const variant = useSelector((state) => state.subscription.currentPlan);
  const subscriptionStatus = useSelector(
    (state) => state.subscription.subscriptionStatus,
  );
  const isModalOpen = useSelector(
    (state) => state.subscription.isShowSubscriptionJourny,
  );
  const freePlan = useSelector((state) => state.subscription.freePlan);
  const proPlan = useSelector((state) => state.subscription.proPlan);
  const enterPrisePlan = useSelector(
    (state) => state.subscription.enterPrisePlan,
  );
  const isTrial = useSelector((state) => state.subscription.isTrial);
  const billingCycle = useSelector((state) => state.subscription.billingCycle);
  const subscriptionJourney = useSelector(
    (state) => state.subscription.subscriptionJourney,
  );
  const getFetureDetails = () => {
    const featureDetails = features.features.find(
      (f) => f.key === subscriptionJourney.feature,
    );
    setFeatureDetails(featureDetails);
  };

  function getPriceByBillingCycle() {
    const productPrice = get(proPlan, 'productPrice', []);
    const result = productPrice.find((price) => price.billing === billingCycle);
    setSelectedPlanDetails(result);
  }

  useEffect(() => {
    getFetureDetails();
    getPriceByBillingCycle();

    /************For directly show payment screen in modal if we have any campaigne*******************/
    if (subscriptionJourney.isCampaign) {
      setCurrentScreen('paymentScreen');
    }
  });

  const router = useRouter();
  const dispatch = useDispatch();
  useEffect(() => {
    return () => {
      setCurrentScreen('showPlans');
    };
  }, [isModalOpen]);

  const handleSubscription = (plan) => {
    setCurrentPlan(plan);
    if (plan === 'free') {
      if (variant !== 'free') {
        const payload = {
          planId: get(freePlan, '_id', ''),
          // priceId: get(freePlan, 'productPriceId', ''),
        };
        dispatch(getClientSecretKey(payload)).then((data) => {
          if (get(data, 'statusCode') === 201) {
            window.location.reload();
          }
        });
      }
      closeModal();
    } else if (plan === 'pro') {
      if (variant === 'pro' && !isTrial && subscriptionStatus !== 'expired') {
        closeModal();
      } else {
        setCurrentScreen('paymentScreen');
      }
    } else {
      dispatch(
        setSubscriptionJourneyModal({
          ...subscriptionJourney,
          modalStatus: false,
        }),
      );
      if (variant !== 'enterprise') {
        router.push({
          pathname: '/profile/bookSlot',
          query: { enterPrise: 'true' },
        });
      }
    }
  };
  const fetchSubscription = async () => {
    await dispatch(getSubscription());
    dispatch(setBillingCycle('monthly'));
    console.log('subscriptionJourney', subscriptionJourney);
    subscriptionJourney.onComplete();
  };
  const closeModal = () => {
    console.log('called------------>>>');
    dispatch(
      setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      }),
    );
    dispatch(setBillingCycle('monthly'));
    setCurrentScreen('showPlans');
  };

  /************ get subscription success button value*******************/
  let subscriptionBtnValue = '';
  switch (get(featureDetails, 'key')) {
    case 'unlimitedProjects':
      subscriptionBtnValue = 'Create new project';
      break;
    case 'watchList':
      subscriptionBtnValue = 'Discover Projects';
      break;
    case 'aiTools':
      subscriptionBtnValue = 'Use AI Tools';
      break;
    case 'discovery':
      subscriptionBtnValue = 'Discover Projects';
      break;
    case 'gettyImageLibrary':
      subscriptionBtnValue = 'Use Getty Images';
      break;
    case 'projectCollaborators':
      subscriptionBtnValue = 'Collaborate on Projects';
      break;
    default:
      subscriptionBtnValue = 'Continue';
      break;
  }

  return (
    featureDetails?.modalTitle && (
      <AiModal
        subHeading={featureDetails?.modalSubHeading}
        show={subscriptionJourney?.modalStatus}
        closeCallback={closeModal}
        title={featureDetails?.modalTitle}
        subcriptionTime={true}
        currentScreen={currentScreen}
        feature={get(subscriptionJourney, 'feature')}
        bodyContent={
          <>
            {currentScreen === 'paymentScreen' && (
              <PaymentScreen
                setShowPaymentScreen={setCurrentScreen}
                plan={currentPlan}
                proPlan={proPlan}
                selectedPlanDetalils={selectedPlanDetalils}
                enterPrisePlan={enterPrisePlan}
                freePlan={freePlan}
                campaignDetails={subscriptionJourney.campaignDetails}
                billingCycle={billingCycle}
              />
            )}

            {currentScreen === 'showPlans' && (
              <ShowPlans
                subscriptionStatus={subscriptionStatus}
                isTrial={isTrial}
                proPlan={proPlan}
                selectedPlanDetalils={selectedPlanDetalils}
                enterPrisePlan={enterPrisePlan}
                freePlan={freePlan}
                handleSubscription={handleSubscription}
                variant={variant}
                subscriptionJourney={subscriptionJourney}
                billingCycle={billingCycle}
              />
            )}

            {currentScreen === 'confirmPayment' && (
              <div
                style={{
                  display: 'flex',
                  padding: '48px',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  // gap: '48px',
                  alignSelf: 'stretch',
                  border: '2px solid #05012D',
                  background: '#FFF',
                }}
              >
                <SmashSubscriptionWelcome
                  plan="pro"
                  headingText="Welcome to"
                  description1="Thank you for upgrading! Enjoy unlimited access to all premium features as part of your Smash Pro membership. We’re excited to have you on board!"
                  buttonText={subscriptionBtnValue}
                  // buttonType="primary-button"
                  buttonType="commonBtnClas"
                  onButtonClick={fetchSubscription}
                />
              </div>
            )}
          </>
        }
      />
    )
  );
}

export default SubscriptionJourney;
