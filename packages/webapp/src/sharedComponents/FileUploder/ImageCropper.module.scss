// ImageCropper.module.scss

.overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow-y: auto;
}

.modal {
  width: 100%;
  max-width: 540px;
  max-height: 640px;
  background: #fff;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  @media (max-width: 576px) {
    max-width: 100%;
    height: auto;
    max-height: 95vh;
  }
}

.header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
  background-color: #fff;

  .title {
    font-size: 16px !important;
    font-weight: 700 !important;
    color: #05012d !important;
    font-family: 'chaney' !important;
  }

  .closeBtn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background: #f5f5f5;
      color: #333;
    }
  }
}

.cropperArea {
  background: #f8f8f8;
  padding: 24px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  // min-height: 400px;
  // max-height: 400px;
  overflow: hidden;
  position: relative;

  @media (max-width: 576px) {
    padding: 20px 5px;
    min-height: 220px;
  }

  // Ensure cropper image fits inside area
  .react-advanced-cropper__background,
  .react-advanced-cropper__image {
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    width: auto !important;
    height: auto !important;
    display: block;
    margin: 0 auto;
  }
}

.controls {
  padding: 16px 20px;
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  justify-content: space-between;

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 10px;
    padding: 12px 8px;
  }
}

.zoomBtn {
  background: none;
  border: none;
  font-size: 20px;
  color: #333;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #f0f0f0;
    color: #000;
  }

  &:disabled {
    color: #ccc;
    cursor: not-allowed;
  }
}

.slider {
  flex: 1;
  height: 3px;
  background: #e0e0e0;
  border-radius: 2px;
  margin: 0 10px;
  cursor: pointer;
  appearance: none;

  &::-webkit-slider-thumb,
  &::-moz-range-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: #333;
    border: 2px solid #fff;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }
  }
}

.applyBtn {
  background: #2c3e50;
  color: #fff;
  border: none;
  padding: 10px 24px;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 4px;
  text-transform: uppercase;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(44, 62, 80, 0.2);

  &:hover {
    background: #34495e;
    box-shadow: 0 4px 8px rgba(44, 62, 80, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    background: #1a252f;
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(44, 62, 80, 0.2);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.2);
  }

  @media (max-width: 576px) {
    width: 100%;
  }
}
