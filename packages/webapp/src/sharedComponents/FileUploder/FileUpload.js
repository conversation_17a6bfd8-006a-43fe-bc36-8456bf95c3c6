import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ReactS3Uploader from 'react-s3-uploader';
import { error } from 'sharedComponents/Alerts/error';
import { validateFile, acceptFileTypeString, getSignedUrl } from 'utils/helper';
import ImageCropper from './ImageCropper';

const FileUpload = ({
  isOriginalName = false,
  name = 'file',
  accept = ['jpeg', 'jpg', 'png'],
  onSuccessHandler,
  onProgressHandler,
  onPreprocessHandler,
  enableCropping = true,
  cropShape,
  cropHeight = '100%',
  className = '',
  children,
}) => {
  const [showCropper, setShowCropper] = useState(false);
  const [imageToCrop, setImageToCrop] = useState(null);
  const [cropCallback, setCropCallback] = useState(null);
  const [originalFile, setOriginalFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);

  const isPdfOnly = accept.length === 1 && accept[0].toLowerCase() === 'pdf';

  const onPreprocess = (file, next) => {
    if (!validateFile(file, accept)) return false;

    if (enableCropping && !isPdfOnly) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImageToCrop(e.target.result);
        setCropCallback(() => next);
        setOriginalFile(file);
        setShowCropper(true);
      };
      reader.readAsDataURL(file);
      return false;
    }

    onPreprocessHandler?.(file, name);
    return next(file);
  };

  const handleCropComplete = (blob) => {
    if (!blob) return error('Cropping failed.');

    const croppedFile = new File([blob], originalFile.name, {
      type: blob.type,
      lastModified: Date.now(),
    });

    onPreprocessHandler?.(croppedFile, name);
    cropCallback?.(croppedFile);
    resetCropper();
  };

  const resetCropper = () => {
    setShowCropper(false);
    setImageToCrop(null);
    setCropCallback(null);
    setOriginalFile(null);
  };

  const handleSignedUrl = async (file, callback) => {
    const signedUrl = await getSignedUrl(file);
    if (signedUrl) callback(signedUrl);
  };

  const onProgress = (percent) => {
    setIsUploading(true);
    onProgressHandler?.(percent, name);
  };

  const onFinish = (res) => {
    setIsUploading(false);
    if (!res?.publicUrl) return error('Upload failed');
    const result = isOriginalName
      ? {
          [name]: {
            url: res.publicUrl,
            name: res.originalFilename || res.filename,
            size: res.size,
            type: res.contentType,
          },
        }
      : { [name]: res.publicUrl };
    onSuccessHandler?.(result);
  };

  const onError = (err) => {
    console.error(err);
    setIsUploading(false);
    error(err?.message || 'Upload failed');
  };

  return (
    <>
      <ReactS3Uploader
        className={`uploadStyleBtn ${className}`}
        getSignedUrl={handleSignedUrl}
        accept={acceptFileTypeString(accept)}
        preprocess={onPreprocess}
        onProgress={onProgress}
        onError={onError}
        onFinish={onFinish}
        multiple={false}
        contentDisposition="auto"
        style={{
          opacity: isUploading ? 0.7 : 1,
          pointerEvents: isUploading ? 'none' : 'auto',
        }}
      />
      {children}
      {showCropper && imageToCrop && !isPdfOnly && (
        <ImageCropper
          imageSrc={imageToCrop}
          isVisible={showCropper}
          onCropComplete={handleCropComplete}
          onCancel={resetCropper}
          cropShape={cropShape}
          cropHeight={cropHeight}
        />
      )}
    </>
  );
};

FileUpload.propTypes = {
  isOriginalName: PropTypes.bool,
  name: PropTypes.string,
  accept: PropTypes.array,
  onSuccessHandler: PropTypes.func.isRequired,
  onProgressHandler: PropTypes.func,
  onPreprocessHandler: PropTypes.func,
  enableCropping: PropTypes.bool,
  cropShape: PropTypes.oneOf(['rect', 'round']),
  cropHeight: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  className: PropTypes.string,
  children: PropTypes.node,
};

export default FileUpload;
