.imageWrapper {
  position: relative;
  width: 100%;
  height: 250px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  overflow: hidden;
  border: 1px solid #dbdbcf;
}

.coverDp {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.feedback {
  position: absolute;
  top: 16px;
  left: 16px;
  border-radius: 10px;
  z-index: 11;
}

.snapCard {
  position: absolute;
  left: 16px;
  bottom: 10px;
  z-index: 12;

  @media (max-width: 768px) {
    left: 20px;
    bottom: 10px;
  }
}

.overlay {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.4);
  color: #ffffff;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.boxContainer {
  padding: 24px;
  width: 100%;
  height: 150px; /* Fixed height to maintain grid consistency */
  background-color: #ecece0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  border: 1px solid #dbdbcf;
  overflow: hidden; /* Hide overflow content */
}

.gridContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 0; /* Ensure no bottom margin */
}

.gridContainer > * {
  flex: 0 0 calc(33.333% - 11px);
  max-width: calc(33.333% - 11px);
  box-sizing: border-box;
  margin-bottom: 0; /* Ensure no bottom margin on grid items */
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .gridContainer {
    justify-content: center;
    margin-bottom: 0; /* Ensure no bottom margin on mobile */
  }

  .gridContainer > * {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 0; /* Ensure no bottom margin on mobile grid items */
  }
}

.cardWrapper {
  display: flex;
  flex-direction: column;
  height: 400px; /* Fixed height for grid consistency */
  /* Removed margin-bottom */
}

.metadata {
  margin-left: 12px;
  width: calc(100% - 60px);
  height: 100px; /* Fixed height for grid consistency */
  overflow: hidden; /* Hide overflow content */
  position: relative; /* For absolute positioning of children */
}

.metadata h4 {
  margin: 0;
  line-height: 1.3;
  font-size: 16px;
  word-break: break-word;
  max-height: 40px; /* Fixed height for title - allows for 2 lines */
  overflow: hidden; /* Hide overflow content */
}

.metadata span {
  display: block;
  position: absolute;
  top: 44px; /* Fixed position from top - always appears at same place */
  left: 0;
  color: #666;
  word-break: break-word;
  max-height: 18px; /* Fixed height for company name */
  overflow: hidden; /* Hide overflow content */
  width: 100%;
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
}

.metadata p {
  position: absolute;
  bottom: 0;
  left: 0;
  margin: 0;
  line-height: 1.3;
  font-size: 12px;
  color: #777;
  width: 100%;
}
