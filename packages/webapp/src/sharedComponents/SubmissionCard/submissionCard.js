// src/components/SubmissionCard/SubmissionCard.jsx
import React from 'react';
import styles from './submissionCard.module.scss';
import { get } from 'lodash';
import Badge from 'sharedComponents/Badge/badge';
import UserAvatar from 'sharedComponents/UserAvatar/userAvatar';
import { goToSnap, statusConfig } from 'utils/helper';

const SubmissionCard = ({ submission }) => {
  // Return null if no submission data
  if (!submission) return null;

  // Parse snapshot body once - simplified
  const parseSnapshotBody = () => {
    try {
      // Get snapshot data from different possible locations
      const snapshotData =
        get(submission, 'submissions.snapshot') || get(submission, 'snapshot');
      if (!snapshotData) return {};

      // Try to parse body if it exists
      if (!snapshotData.body) return {};

      // Handle string body (parse JSON)
      if (typeof snapshotData.body === 'string') {
        try {
          const body = JSON.parse(snapshotData.body);
          return body;
        } catch (e) {
          // Silent error handling
          return {};
        }
      }

      // Handle object body (use directly)
      if (typeof snapshotData.body === 'object') {
        return snapshotData.body;
      }

      return {};
    } catch (error) {
      return {};
    }
  };

  const isSlate = get(submission, 'type') === 'slate';
  const snapShotBody = parseSnapshotBody();

  // Get data using simplified lodash get with fallbacks
  // Simplified title extraction
  const title =
    get(submission, 'submissions.snapshot.title') ||
    get(snapShotBody, 'cover.title') ||
    get(submission, 'snapshot.title') ||
    get(submission, isSlate ? 'slate.title' : 'title', 'Untitled');

  // Simplified author extraction
  const author =
    get(submission, 'submissions.creator.username') ||
    get(submission, 'creator.username', 'Unknown');

  // Simplified status extraction
  const status =
    get(submission, 'submissions.status') || get(submission, 'status', '');

  const statusDetails = statusConfig[status] || {
    label: status || 'Pending',
    className: '',
    backgroundColor: '#ccc',
    textColor: '#333',
  };

  // Simplified submittedDate extraction
  const submittedDate =
    get(submission, 'submissions.addedAt') ||
    get(submission, 'createdAt', new Date().toISOString());

  // Prioritize companyName from submission (which comes from callout)
  const companyName =
    get(submission, 'companyName') ||
    get(snapShotBody, 'cover.title') ||
    get(submission, 'submissions.snapshot.title') ||
    get(submission, 'snapshot.title', '');

  // Prioritize coverUrl from submission (which comes from callout logo)
  const coverUrl =
    get(submission, 'coverUrl') ||
    get(submission, 'submissions.snapshot.coverPic') ||
    get(snapShotBody, 'cover.coverPic') ||
    get(submission, isSlate ? 'slate.coverPic' : 'snapshot.coverPic') ||
    get(submission, 'coverPic', '');

  // Simplified userAvatar extraction
  const userAvatar =
    get(submission, 'submissions.creator.avatar') ||
    get(submission, 'creator.avatar', null);

  // Get callout name directly from submission data
  const displayCalloutName =
    get(submission, 'calloutName') ||
    get(submission, 'callout.name', 'Untitled Callout');

  const theme = isSlate ? '' : get(snapShotBody, 'theme', '');

  return (
    <div className={styles.cardWrapper}>
      <div
        className={styles.imageWrapper}
        key={submission._id}
        onClick={() => goToSnap(submission)}
        style={{ cursor: 'pointer' }}
      >
        {coverUrl ? (
          <img
            className={`${styles.coverDp}`}
            src={coverUrl}
            width={100}
            height={100}
            alt="cover"
          />
        ) : (
          <img
            className={`${styles.coverDp}`}
            src={`${process.env.BucketUrl}ASSETS/${
              theme === 'dark' || theme === 'light'
                ? 'darkLightDmDefaultCover.jpg'
                : 'dmBcsDefaultCover.jpg'
            }`}
            width={100}
            height={100}
            alt="default cover"
          />
        )}
        <div className={styles.feedback}>
          {statusDetails && (
            <Badge
              list={[
                {
                  label: statusDetails.label,
                  className: `badge-btn fs-12 ${statusDetails.className}`,
                  style: {
                    backgroundColor: statusDetails.backgroundColor,
                    color: statusDetails.textColor,
                  },
                },
              ]}
            />
          )}
        </div>
        <div className={styles.overlay}>
          <div className={styles.snapCard}>
            <h4>{title}</h4>
            <p className="p2 mt-1">by {author}</p>
          </div>
        </div>
      </div>

      <div className={styles.boxContainer}>
        <h4 className="fw-bold text-uppercase" title={displayCalloutName}>
          {displayCalloutName}
        </h4>

        <div className="d-flex align-items-start">
          {/* Avatar */}
          <UserAvatar src={userAvatar} alt={author} size="md" />
          {/* Title + Metadata block */}
          <div style={{ marginLeft: '12px' }}>
            <p className="mb-1 p2">{companyName}</p>
            <p className="text-muted mb-0 p2">
              Submitted:
              {new Date(submittedDate).toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'long',
                year: 'numeric',
              })}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmissionCard;
