import React from 'react';
import PropTypes from 'prop-types';
import Router from 'next/router';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';
import Button from 'sharedComponents/Button/button';
import CalloutCard from './CalloutCard';

/**
 * CarouselSection - A reusable component for displaying a section with a title and carousel
 *
 * @param {string} title - The section title
 * @param {Array} items - The items to display in the carousel
 * @param {string} type - The type of callout ('my' or 'discover')
 * @param {number} maxItems - Maximum number of items to display
 * @param {number} slidesPerPage - Number of slides per page
 * @param {boolean} showViewMore - Whether to show the "View More" button
 * @param {string} viewMoreUrl - URL to navigate to when "View More" is clicked
 * @returns {JSX.Element} - Rendered component
 */
const CarouselSection = ({
  title,
  items,
  type,
  maxItems = 10,
  slidesPerPage = 3,
  showViewMore = true,
  viewMoreUrl = '/discoverer/dashboard',
}) => {
  if (!items || items.length === 0) {
    return null;
  }

  const handleViewMore = () => {
    // Use Router.push with shallow option to prevent full page reload
    Router.push(viewMoreUrl, undefined, { shallow: false });
  };

  // Check if there are additional items to show
  const hasAdditionalItems = items.length > slidesPerPage;

  return (
    <div className="row mt-32">
      <div className="col-12">
        <SectionHeader
          title={title}
          showButton={showViewMore && hasAdditionalItems}
          buttonText="View All"
          customClass="viewListBtn"
          alignment="left"
          onButtonClick={handleViewMore}
          headerTitleClass="fs-20"
        />

        <div className="pt-3 pb-5 border-bottom">
          <CustomCarousel
            items={items.slice(0, maxItems)}
            renderItem={(calloutItem) => (
              <CalloutCard
                key={calloutItem._id}
                calloutItem={calloutItem}
                type={type}
              />
            )}
            slidesPerPage={slidesPerPage}
            autoplay={false}
          />
        </div>

        {/* Mobile View More Button */}
        {showViewMore && hasAdditionalItems && (
          <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom">
            <Button
              btntype="button"
              customClass="viewListBtn"
              className="w-100 py-2 px-3"
              buttonValue="View All"
              clickHandler={handleViewMore}
              isActive={false}
            />
          </div>
        )}
      </div>
    </div>
  );
};

CarouselSection.propTypes = {
  title: PropTypes.string.isRequired,
  items: PropTypes.array.isRequired,
  type: PropTypes.oneOf(['my', 'discover']).isRequired,
  maxItems: PropTypes.number,
  slidesPerPage: PropTypes.number,
  showViewMore: PropTypes.bool,
  viewMoreUrl: PropTypes.string,
};

export default CarouselSection;
