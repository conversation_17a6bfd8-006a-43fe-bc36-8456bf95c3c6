import React, { useState, useEffect } from 'react';
import { get } from 'lodash';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import ProjectCard from 'sharedComponents/ProjectCard/projectCard';
import Button from 'sharedComponents/Button/button';
import Loader from 'sharedComponents/loader';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';

/**
 * InterestedProjects - A component to display projects the user is interested in
 *
 * @returns {JSX.Element} - Rendered component
 */
const InterestedProjects = () => {
  const router = useRouter();
  const dmProjectsList = useSelector(
    (state) => state.project.dmProjectsList || [],
  );
  const userData = useSelector((state) => state.auth.userData);
  const isLoading = useSelector((state) => state.project.isLoading);
  const [interestedProjects, setInterestedProjects] = useState([]);

  // Process DM projects to filter only interested projects
  // Using a ref to track if we've already processed the projects to prevent unnecessary re-renders
  const processedRef = React.useRef(false);

  useEffect(() => {
    // Only process if we have both dmProjectsList and userData.email, and haven't processed yet
    if (dmProjectsList?.length && userData?.email && !processedRef.current) {
      const interested = [];

      dmProjectsList.forEach((item) => {
        const email = userData.email;

        const userActivities = item.activities.filter(
          (snapData) => snapData.user.email === email,
        );

        if (userActivities.length) {
          // Create a copy with filtered activities
          const itemWithFilteredActivities = {
            ...item,
            activities: userActivities,
          };

          userActivities.forEach((snapData) => {
            if (
              snapData.action === 'letsTalk' ||
              snapData.action === 'tracking'
            ) {
              interested.push(itemWithFilteredActivities);
            }
          });
        }
      });

      setInterestedProjects(interested);
      // Mark as processed to prevent unnecessary re-renders
      processedRef.current = true;
    }
  }, [dmProjectsList, userData]);

  const handleViewMore = () => {
    router.push('/discoverer/dashboard');
  };

  const goToSnap = (hash) => {
    if (hash) {
      window.open(`/project/snap/${hash}`, '_blank');
    }
  };

  const renderProjectCard = (item) => {
    const body = get(item, 'body');
    const parsedBody = body ? JSON.parse(body) : {};
    const theme = get(parsedBody, 'theme');
    const title = get(parsedBody, 'cover.title', 'Untitled Project');
    const basicInfo = get(parsedBody, 'basicInfo', {});
    const projectTags = get(basicInfo, 'tags', []);
    const validProjectTags = Array.isArray(projectTags) ? projectTags : [];

    return (
      <ProjectCard
        key={item._id}
        item={item}
        theme={theme}
        title={title}
        basicInfo={basicInfo}
        projectTags={validProjectTags}
        onClick={() => {
          goToSnap(item.hash);
        }}
      />
    );
  };

  // Only show this section if there are interested projects
  if (interestedProjects.length === 0 && !isLoading) {
    return null;
  }

  // Always show the View More button when there are projects
  const hasProjects = interestedProjects.length > 0;

  return (
    <div className="row mt-3 mt-md-5 mt-lg-5">
      <div className="col-12">
        <SectionHeader
          title="Watch list"
          showButton={hasProjects}
          buttonText="View All"
          customClass="viewListBtn"
          onButtonClick={handleViewMore}
          alignment="left"
          showBackButton={false}
          headerTitleClass="fs-20"
        />

        {isLoading ? (
          <div className="text-center py-4">
            <Loader />
            <p className="mt-3">Loading projects...</p>
          </div>
        ) : (
          <>
            {interestedProjects.length > 0 ? (
              <div className="py-3 pt-md-4 pb-5 pt-md-4 border-bottom">
                {/* Always show carousel mode since we redirect instead of showing grid */}
                <CustomCarousel
                  items={interestedProjects.slice(0, 9)}
                  renderItem={renderProjectCard}
                  slidesPerPage={3}
                  autoplay={false}
                />
              </div>
            ) : (
              <p className="text-center text-primary mt-4 border-bottom pb-4">
                No projects here.
              </p>
            )}

            {/* Mobile View More Button */}
            {hasProjects && (
              <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom">
                <Button
                  btntype="button"
                  customClass="viewListBtn"
                  className="w-100 py-2 px-3"
                  buttonValue="View All"
                  clickHandler={handleViewMore}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default InterestedProjects;
