import React from 'react';
import PropTypes from 'prop-types';
import Router from 'next/router';
import { get } from 'lodash';
import UserAvatar from 'sharedComponents/UserAvatar/userAvatar';

/**
 * CalloutCard - A reusable component for displaying callout items
 *
 * @param {Object} calloutItem - The callout data to display
 * @param {string} type - The type of callout ('my' or 'discover')
 * @returns {JSX.Element} - Rendered component
 */
const CalloutCard = ({ calloutItem, type }) => {
  const handleClick = () => {
    // Use _id instead of id, and add a fallback
    const calloutId = calloutItem._id || calloutItem.id;
    if (type === 'my') {
      Router.push(`/callouts/${calloutId}/slates`, undefined, {
        shallow: false,
      });
    } else {
      Router.push(`/callouts/${calloutId}`, undefined, { shallow: false });
    }
  };

  const userAvatar =
    type === 'discover'
      ? get(calloutItem, 'discoverer.avatar', null)
      : get(calloutItem, 'creator.avatar', null);

  const author =
    type === 'discover'
      ? get(calloutItem, 'discoverer.username', 'Unknown')
      : get(calloutItem, 'creator.username', 'Unknown');

  return (
    <div
      key={calloutItem._id}
      className="callout-carousel-item"
      style={{ height: '100%' }}
    >
      <div
        onClick={handleClick}
        style={{
          backgroundColor: '#ecece0',
          border: '1px solid #dbdbcf',
          borderRadius: '6px',
          height: '100%',
          cursor: 'pointer',
        }}
      >
        <div
          style={{
            width: '100%',
            height: '268px',
            borderRadius: '6px',
            overflow: 'hidden',
            position: 'relative',
            padding: '12px',
          }}
        >
          <img
            style={{
              objectFit: 'cover',
              width: '100%',
              height: '100%',
              borderRadius: '6px',
              border: '1px solid #dbdbcf',
            }}
            src={get(calloutItem, 'body.logo', '')}
            alt="cover"
          />
        </div>
        <div style={{ padding: '10px' }}>
          <h4 className="mb-1 fw-bold text-uppercase px-12">
            {get(calloutItem, 'name', '')}
          </h4>
          <div className="d-flex align-items-start mt-3 px-12">
            <UserAvatar src={userAvatar} alt={author} size="md" />

            <div style={{ marginLeft: '12px' }}>
              <p className="mb-1 p2">
                {get(calloutItem, 'body.companyName', '')}
              </p>
              <p className="text-muted mb-0 p2">
                {new Date(get(calloutItem, 'createdAt', '')).toLocaleDateString(
                  'en-GB',
                  {
                    day: '2-digit',
                    month: 'long',
                    year: 'numeric',
                  },
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

CalloutCard.propTypes = {
  calloutItem: PropTypes.object.isRequired,
  type: PropTypes.oneOf(['my', 'discover']).isRequired,
};

export default CalloutCard;
