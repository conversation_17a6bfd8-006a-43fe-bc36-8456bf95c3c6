/* eslint-disable no-useless-escape */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable no-lone-blocks */
/* eslint-disable no-unused-expressions */
/* eslint-disable react/button-has-type */
/* eslint-disable jsx-a11y/no-static-element-interactions */

import React from 'react';
import RenderField from 'sharedComponents/renderfield';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { get, assign } from 'lodash';
import { required } from 'validation/commonValidation';
import { Field, reduxForm } from 'redux-form';
import SimpleDropDown from 'sharedComponents/simpleDropDown';
import DropDown from 'sharedComponents/dropDown/dropdown';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import ReactModal from 'sharedComponents/Modal/modal';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import TrashIconSvgPath from 'svgpath/TrashIconSvgPath';
import Style from '../../ProjectTemplate/bcsTemplate/Style/cover.module.scss';
import { preventEnterKey } from 'src/utils/helper';

// Project cover data edit form component
class CoverEdit extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showDropdown: false,
      isShowModal: false,
    };
    this.fileUploadRef = React.createRef();
  }

  componentDidMount() {
    const { projectPreviewData, initialize, setCoverImage } = this.props;
    get(projectPreviewData, 'cover') &&
      initialize({
        title: projectPreviewData.cover.title || '',
        producer: projectPreviewData.cover.producer || '',
        writer: projectPreviewData.cover.writer || '',
        director: projectPreviewData.cover.director || '',
      });
    setCoverImage(projectPreviewData.cover.coverPic || null);

    // Style the file input to be invisible
    if (this.fileUploadRef.current) {
      const fileInput =
        this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.top = '-9999px';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
      }
    }
  }

  // to close the edit section
  cancel = () => {
    const { cancel, setSaveButtonState, setCoverImageUploadStatus } =
      this.props;
    setSaveButtonState(false);
    setCoverImageUploadStatus(false);
    cancel();
  };

  // submit form values
  submitProjectForm = (values) => {
    const { updateCoverInfo, router, coverImg } = this.props;
    const updatedData = assign({}, values, {
      coverPic: coverImg === false || null ? '' : coverImg,
    });
    const id = router.asPath.split('/').pop();
    updateCoverInfo(updatedData, id, 'close');
  };

  // save data which is added by the user
  saveData = () => {
    this.form.dispatchEvent(new Event('submit', { cancelable: true }));
  };

  // open dropdown to add cover image
  toggleDropdown = () => {
    const { showDropdown } = this.state;
    this.setState({ showDropdown: !showDropdown });
  };

  // open dropdown to add cover image
  toggleDropdownCancle = () => {
    this.setState({ showDropdown: false });
  };

  // method to remove uploaded cover image
  removeCoverPicHandler = () => {
    const { deleteFile, router, setCoverImageUrl, setCoverImage } = this.props;
    const id = router.asPath.split('/').pop();
    deleteFile(id, 'coverPic');
    setCoverImageUrl(false);
    setCoverImage(null);
    this.setState({
      isShowModal: false,
    });
  };

  // method to close modal
  closeModalHandler = () => {
    this.setState({
      isShowModal: false,
    });
  };

  // method to open modal
  removeCover = () => {
    this.setState({
      isShowModal: true,
    });
    this.toggleDropdown();
  };

  // method to get url of image
  getUploadedUrl = () => {
    // Trigger the file input directly
    if (this.fileUploadRef.current) {
      const fileInput =
        this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
    this.toggleDropdown();
  };

  render() {
    const {
      t,
      handleSubmit,
      directorList,
      writerList,
      producerList,
      projectPreviewData,
      setCoverImageUploadStatus,
      // coverImgUploadStatus,
      setCoverImageUrl,
    } = this.props;
    const { showDropdown, isShowModal } = this.state;
    const dropdownOptions = [
      {
        icon: UploadButtonSvgPath,
        uploadControl: false,
        handler: this.getUploadedUrl,
        value: 'ADD COVER',
      },
      {
        icon: TrashIconSvgPath,
        uploadControl: false,
        handler: this.removeCover,
        value: 'REMOVE COVER',
      },
    ];
    return (
      <>
        <div className="col-12 p-0">
          <ReactModal
            modalShow={isShowModal}
            title="Dou you want to remove this cover image?"
            body="This action cannot be undone."
            successBtnText="Yes! Remove"
            closeBtnText="Cancel"
            modalSize="md"
            successCallback={this.removeCoverPicHandler}
            closeCallback={this.closeModalHandler}
          />

          <div className=" col-12 m-auto">
            <div
              className={`${Style.trapezoidLeftBottom} bg-danger d-flex justify-content-center align-items-center`}
            >
              <div className="d-flex flex-row mt-5" />
              <div className="col-12">
                <div className="d-flex flex-row justify-content-md-center align-items-center">
                  <p className="d-flex align-self-center text-primary mt-2 pr-2">
                    Cover Image
                  </p>
                  <button
                    type="addImage"
                    id="image"
                    onClick={() => this.toggleDropdown()}
                    className="btn-primary pl-3 pr-3"
                  >
                    add Image
                  </button>
                  {showDropdown && (
                    <div className="mt-5">
                      <DropDown options={dropdownOptions} />
                    </div>
                  )}
                </div>
                <form
                  onSubmit={handleSubmit(this.submitProjectForm)}
                  ref={(n) => {
                    this.form = n;
                  }}
                  onKeyDown={preventEnterKey}
                >
                  <div
                    onClick={() => this.toggleDropdownCancle()}
                    className="col-12 d-flex justify-content-sm-start d-flex justify-content-md-center"
                  >
                    <div className="col-10 mt-4">
                      <Field
                        component={RenderField}
                        name="title"
                        placeholder={t(
                          'common:projectCreate.create.form.title',
                        )}
                        validate={[required]}
                        type="text"
                        className="bg-dark"
                        projectPreviewData={projectPreviewData}
                      />
                    </div>
                  </div>
                  <div className="d-none">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      id="coverSave"
                      // onClick={() => this.saveData()}
                    >
                      SAVE
                    </button>
                    <button
                      type="button"
                      id="coverCancel"
                      className="btn btn-primary"
                      onClick={() => this.cancel()}
                    >
                      CANCEL
                    </button>
                  </div>
                </form>
              </div>
              <div className="d-flex flex-row mt-5" />
            </div>
            <div className={`${Style.coverFooterBox} col-12 m-auto pt-4`}>
              <div className="col-12 sm-inline-flex d-md-flex flex-row pr-0">
                <div className="col-4 sm-inline-flex d-md-flex  flex-row">
                  <h4 className=" h4 text-secondary d-flex justify-content-sm-start justify-content-md-end align-items-center col-auto col-md-4 col-xl-4 col-lg-4 col-sm-auto text-right p-0">
                    Produced by
                  </h4>
                  <div className="col-8">
                    <Field
                      id="producerList"
                      name="producer"
                      component={SimpleDropDown}
                      options={producerList}
                      type="updateCover"
                      projectPreviewData={projectPreviewData}
                      className="bg-dark"
                    />
                  </div>
                </div>
                <div className="col-4 sm-inline-flex d-md-flex  flex-row">
                  <h4 className=" h4 text-secondary d-flex justify-content-sm-start justify-content-md-end align-items-center col-auto col-md-4 col-xl-4 col-lg-4 col-sm-auto text-right p-0">
                    {t('common:projectCreate.preview.directedBy')}
                  </h4>
                  <div className="col-8">
                    <Field
                      id="directorList"
                      name="director"
                      component={SimpleDropDown}
                      options={directorList}
                      type="updateCover"
                      projectPreviewData={projectPreviewData}
                      className="bg-dark"
                    />
                  </div>
                </div>
                <div className="col-4 sm-inline-flex d-md-flex  flex-row">
                  <h4 className=" h4 text-secondary d-flex justify-content-sm-start justify-content-md-end align-items-center col-auto col-md-4 col-xl-4 col-lg-4 col-sm-auto text-right p-0">
                    {t('common:projectCreate.preview.writtenBy')}
                  </h4>
                  <div className="col-7 ">
                    <Field
                      id="writerList"
                      name="writer"
                      component={SimpleDropDown}
                      options={writerList}
                      type="updateCover"
                      projectPreviewData={projectPreviewData}
                      className="bg-dark"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FileUpload component visible but file input styled to be invisible */}
        <div
          ref={this.fileUploadRef}
          data-cy="uploadImageArtwork"
          style={{
            position: 'relative',
            width: '1px',
            height: '1px',
            overflow: 'hidden',
          }} // Keep component in DOM but minimal size
        >
          <FileUpload
            name="coverImage"
            accept={['jpeg', 'jpg', 'png']}
            onSuccessHandler={(result) => {
              const url = result.coverImage?.url || result.coverImage;
              setCoverImageUrl(url);
              setCoverImageUploadStatus(false);
            }}
            onProgressHandler={() => setCoverImageUploadStatus(true)}
            onPreprocessHandler={() => setCoverImageUploadStatus(true)}
            enableCropping={true}
            cropShape="rect"
            cropHeight={320}
            className="uploadStyleBtn"
          />
        </div>
      </>
    );
  }
}
CoverEdit.propTypes = {
  t: PropTypes.func.isRequired,
  router: PropTypes.func.isRequired,
  updateCoverInfo: PropTypes.bool.isRequired,
  producerList: PropTypes.bool.isRequired,
  writerList: PropTypes.bool.isRequired,
  directorList: PropTypes.bool.isRequired,
  projectPreviewData: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
  cancel: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  setSaveButtonState: PropTypes.func.isRequired,
  deleteFile: PropTypes.func.isRequired,
  setCoverImageUrl: PropTypes.func.isRequired,
  setCoverImage: PropTypes.func.isRequired,
  setCoverImageUploadStatus: PropTypes.func.isRequired,
  coverImgUploadStatus: PropTypes.bool.isRequired,
  coverImg: PropTypes.string.isRequired,
};

export default reduxForm({
  form: 'editForm',
})(withTranslation('common')(CoverEdit));
