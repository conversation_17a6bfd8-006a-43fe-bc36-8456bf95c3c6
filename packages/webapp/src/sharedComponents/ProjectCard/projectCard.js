// components/ProjectCard/ProjectCard.jsx
import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import Tags from 'sharedComponents/Tags/tags';
import styles from './projectCard.module.scss';

const ProjectCard = ({
  item,
  theme,
  title,
  basicInfo,
  projectTags,
  onClick,
}) => {
  const coverUrl = get(item, 'projectInfo.coverUrl');
  const defaultCover =
    theme === 'dark' || theme === 'light'
      ? `${process.env.BucketUrl}ASSETS/darkLightDmDefaultCover.jpg`
      : `${process.env.BucketUrl}ASSETS/dmBcsDefaultCover.jpg`;

  const getLogText = (basicInfo) => {
    const logline = get(basicInfo, 'logLine');
    return logline || 'No Logline';
  };

  const validTags = Array.isArray(projectTags) ? projectTags : [];

  return (
    <div onClick={onClick} className="position-relative">
      <div className="position-relative">
        <div className={styles.overlay}>
          <div className={styles.snapCard}>
            <h5 className="text-light fs-16">{title}</h5>
            <p className="p2 text-light">
              by {get(item, 'creator.fullName', '')}
            </p>
          </div>
        </div>

        <img
          className={styles.coverDp}
          src={coverUrl ? coverUrl : defaultCover}
          width={100}
          height={100}
          alt="cover"
        />
      </div>

      <div className={styles.boxContainer}>
        <p className={`p3 ${styles.logText}`}>
          {basicInfo ? getLogText(basicInfo) : 'No Logline'}
        </p>

        {/* Show Tags component always, it will handle "No Tags" display internally */}
        <Tags
          tags={validTags}
          textColor="#ECECE0"
          backgroundColor="#05012D"
          showNoTags
        />
      </div>
    </div>
  );
};

ProjectCard.propTypes = {
  item: PropTypes.object.isRequired,
  theme: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  basicInfo: PropTypes.object,
  projectTags: PropTypes.array,
  onClick: PropTypes.func,
};

export default ProjectCard;
