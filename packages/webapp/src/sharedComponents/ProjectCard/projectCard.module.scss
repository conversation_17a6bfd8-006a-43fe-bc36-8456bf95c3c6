@import '../../styles/variable.scss';

.coverDp {
  object-fit: cover;
  width: 100%;
  background: url('../../../public/assets/gif/imageLoader.gif') no-repeat center;
  height: 250px;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  border: 1px solid #dbdbcf;
}

.overlay {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.4);
  color: #ffffff;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.boxContainer {
  padding: 24px;
  width: 100%;
  background-color: #ecece0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  border: 1px solid #dbdbcf;
}

.logText {
  line-height: 150%;
  height: 108px;
  color: #05012d;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 6; /* Number of lines to show */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.snapCard {
  position: absolute;
  left: 16px;
  z-index: 10;
  bottom: 10px;
  @media (min-width: 100px) and (max-width: 768px) {
    left: 20px;
    z-index: 10;
    bottom: 10px;
  }
}
