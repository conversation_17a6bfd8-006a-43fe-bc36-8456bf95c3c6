import React, { useEffect, useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  useStripe,
  useElements,
  PaymentElement,
} from '@stripe/react-stripe-js';
import {
  applyCouponCode,
  getClientSecretKey,
  getManageSubscriptionLink,
  getPaymentIntent,
  removeCouponCode,
  setBillingCycle,
} from 'reducer/subscription';
import { useDispatch, useSelector } from 'react-redux';
import { getSubscription } from 'reducer/subscription';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import Loader from 'sharedComponents/loader';
import { get, isEmpty } from 'lodash';
import Button from 'sharedComponents/CascadeTemplate/Common/Button/button';
import Styles from './paymentForm.module.scss';
import Tag from 'sharedComponents/Tag/Tag';
import PlanDetails from 'sharedComponents/PricingCard/planDetails';

const stripePromise = loadStripe(process.env.stripePublicKey);

const PaymentScreen = ({
  clientSecret,
  setShowPaymentScreen,
  getPaymentIntent,
  applyCouponCode,
  planDetail,
  removeCouponCode,
  setPriceText,
  selectedPlanDetalils,
  campaignDetails,
  setPromoCodeDetails,
  setBillingCycle,
  subscriptionId,
  setClientSecret,
}) => {
  const stripe = useStripe();
  const elements = useElements();

  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMsg, setErrorMessage] = useState(null);
  const [promoCode, setPromoCode] = useState('');
  const [applyText, setApplyText] = useState(true);
  const [couponErrorMsg, setCouponErrorMsg] = useState(null);
  const [isCodeApplied, setIsCodeApplied] = useState(false);
  const [inCodeRemoving, setIsCodeRemoving] = useState(false);
  const [isOfferScreen, setIsOfferScreen] = useState(false);

  const dispatch = useDispatch();

  useEffect(() => {
    setIsOfferScreen(!isEmpty(campaignDetails));
    if (!isEmpty(campaignDetails)) {
      setPromoCode(get(campaignDetails, 'couponCode', null));
      if (get(campaignDetails, 'couponCode', null)) {
        onCodeApply(null, campaignDetails);
      }
    }
  }, []);

  const chargeUser = async (event) => {
    setErrorMessage(null);
    setCouponErrorMsg(null);
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      // Stripe.js has not yet loaded or clientSecret is not available
      return;
    }

    setIsProcessing(true);
    try {
      const { error: submitError } = await elements.submit();

      if (submitError) {
        console.error(
          'Form validation error:',
          get(
            submitError,
            'message',
            'Something went wrong please try again later.',
          ),
        );
        setIsProcessing(false);
        return;
      }

      const { error } = await stripe.confirmPayment({
        elements,
        clientSecret,
        redirect: 'if_required',
      });
      if (error) {
        console.error(
          'Payment confirmation error:',
          get(error, 'message', 'Something went wrong please try again later.'),
        );
        setErrorMessage(
          get(error, 'message', 'Something went wrong please try again later.'),
        );
        setIsProcessing(false);
      } else {
        setTimeout(async () => {
          const customerPaymentIntent = await getPaymentIntent();
          if (
            get(customerPaymentIntent, 'data[0].status') ===
            'requires_action' ||
            get(customerPaymentIntent, 'data[0].status') ===
            'requires_confirmation'
          ) {
            const { error } = await stripe.confirmCardPayment(
              get(customerPaymentIntent, 'data[0].client_secret'),
            );
            if (error) {
              console.error('Payment confirmation error:', error.message);
              setErrorMessage(error.message);
              setIsProcessing(false);
            } else {
              dispatch(getSubscription());
              if (isOfferScreen) {
                if (typeof window !== 'undefined') {
                  setTimeout(() => {
                    window.location.href = '/dashboard';
                  }, 1000);
                }
              } else {
                setShowPaymentScreen('confirmPayment');
              }
              setBillingCycle('monthly');
              setIsProcessing(false);
            }
          } else {
            dispatch(getSubscription());
            if (isOfferScreen) {
              if (typeof window !== 'undefined') {
                setTimeout(() => {
                  window.location.href = '/dashboard';
                }, 1000);
              }
            } else {
              setShowPaymentScreen('confirmPayment');
            }
            setIsProcessing(false);
          }
        }, 2000);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
    }
  };

  const onCodeApply = async (event, campaignDetails) => {
    if (event) {
      event.preventDefault();
    }
    setApplyText(false);
    const requestData = {
      subscriptionId,
      ...(campaignDetails
        ? {
          couponCode: campaignDetails.couponCode,
          planId: campaignDetails.planId,
          priceId: campaignDetails.priceId,
        }
        : {
          couponCode: promoCode,
          planId: get(planDetail, '_id', ''),
          priceId: get(selectedPlanDetalils, 'priceId', ''),
        }),
    };
    const data = await applyCouponCode(requestData);
    console.log('Coupon code response:', data); // Debug log

    // Check if data is a string (error message) or object (success response)
    if (typeof data === 'string') {
      // Error case - data is the error message
      setPriceText(null);
      setApplyText(true);
      setCouponErrorMsg(data);
    } else if (data && get(data, 'clientSecret', '') !== '') {
      // Success case - data is an object with coupon details
      setCouponErrorMsg(null);
      setClientSecret(get(data, 'clientSecret', ''));

      // Ensure couponDetails is a valid object before setting it
      const couponDetails = get(data, 'couponDetails', {});
      if (couponDetails && typeof couponDetails === 'object' && !Array.isArray(couponDetails)) {
        setPromoCodeDetails(couponDetails);
      } else {
        setPromoCodeDetails({});
      }

      setIsCodeApplied(true);
      setApplyText(false);
      setPriceText(get(data, 'amount', ''));
    } else {
      // Fallback error case
      setPriceText(null);
      setApplyText(true);
      setCouponErrorMsg('Invalid or expired promotional code.');
    }
  };

  const handleCodeChange = (e) => {
    setCouponErrorMsg(null);
    const trimmedValue = e.target.value.replace(/\s+/g, '');
    setPromoCode(trimmedValue);
  };

  const handleRemove = async (event) => {
    setIsCodeRemoving(true);
    event.preventDefault();
    const data = await removeCouponCode({
      subscriptionId,
      planId: get(planDetail, '_id', ''),
      priceId: get(selectedPlanDetalils, 'priceId', ''),
    });
    if (get(data, 'clientSecret', '') !== '') {
      setClientSecret(get(data, 'clientSecret', ''));
      setPromoCodeDetails(null);
      setPriceText(null);
      setCouponErrorMsg(null);
      setIsCodeApplied(false);
      setApplyText(true);
      setIsCodeRemoving(false);
    } else {
      setIsCodeRemoving(false);
    }
  };

  return (
    <form
      onSubmit={chargeUser}
      className={`${Styles.paymentLabel} ${Styles.paymentForm} p-4 p-sm-4 p-md-5 p-lg-5`}
    >
      <div style={{ width: '100%' }}>
        <p className={`${Styles.formHeading} mb-4 mx-0 mt-0`}>
          Enter payment details
        </p>
        <div className="form-group">
          <label style={{ fontFamily: 'MaisonNeue' }} htmlFor="promo-code">
            Promotion code
          </label>
          <div className="row gx-3 align-items-center mx-0">
            <div className="col-12 col-md pl-0 pr-0 pr-md-2">
              <input
                style={{
                  opacity: !applyText ? '0.6' : '1',
                }}
                value={promoCode}
                className={`${Styles.promotionCodeInput}`}
                disabled={isCodeApplied}
                onChange={handleCodeChange}
                type="text"
                id="promo-code"
                placeholder="Got a promotional code?"
              />
            </div>
            <div className="col-12 col-md-auto px-0">
              <Button
                customClass={Styles.submitButton}
                style={{
                  opacity:
                    !applyText ||
                      !promoCode ||
                      promoCode === '' ||
                      isCodeApplied
                      ? '0.2'
                      : '1',
                }}
                clickHandler={
                  applyText && promoCode && promoCode !== '' && !isCodeApplied
                    ? (e) => onCodeApply(e)
                    : (event) => {
                      event.preventDefault();
                    }
                }
                buttonType="commonBtnClas"
                disabled={!applyText}
                id="submitbutton"
                buttonValue={'Apply'}
                className={`save-btn  px-4 py-2 w-100 w-md-auto ${Styles.applyText}`}
              />
            </div>
          </div>
        </div>
        {isCodeApplied && (
          <Tag
            label={promoCode}
            onRemove={
              inCodeRemoving
                ? (event) => {
                  event.preventDefault();
                }
                : (e) => handleRemove(e)
            }
          />
        )}
        {couponErrorMsg && (
          <p className={Styles.couponErrMsg}> {couponErrorMsg}</p>
        )}

        <div className={Styles.stripePaymentElementForm}>
          <PaymentElement id="card-element" />
        </div>

        <p className={`mt-3 ${Styles.autoRenewText}`}>
          {isOfferScreen
            ? `Auto renews at £${(get(selectedPlanDetalils, 'amount', '') / 100).toFixed(2)}. Cancel anytime.`
            : 'Auto renewal enabled. Cancel anytime.'}
        </p>
        {errorMsg && <p className={Styles.paymentErrMsg}> {errorMsg}</p>}
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button
            customClass={Styles.submitButton}
            style={{ opacity: isProcessing ? '0.2' : '1' }}
            // buttonType="primary-button"
            buttonType="commonBtnClas"
            disabled={isProcessing || couponErrorMsg}
            btntype="submit"
            id="submitbutton"
            buttonValue={
              isProcessing ? 'Processing...' : 'UPGRADE TO SMASH PRO'
            }
            className="save-btn mt-auto w-100"
          />
        </div>
      </div>
    </form>
  );
};

// Wrap the PaymentScreen component with Elements provider
const PaymentWrapper = ({
  getClientSecretKey,
  setShowPaymentScreen,
  getPaymentIntent,
  getManageSubscriptionLink,
  applyCouponCode,
  removeCouponCode,
  setPriceText,
  priceText,
  selectedPlanDetalils,
  campaignDetails,
  setPromoCodeDetails,
  promoCodeDetails,
}) => {
  const [clientSecret, setClientSecret] = useState(null);
  const [subscriptionId, setSubscriptionId] = useState(null);
  const planDetail = useSelector((state) => state.subscription.proPlan);
  // const [customerID, setCustomerID] = useState(null);
  useEffect(() => {
    if (!planDetail && !get(planDetail, '_id', null) && !selectedPlanDetalils) {
      return;
    }

    const payload = {
      id: get(planDetail, '_id', ''),
      priceId: get(selectedPlanDetalils, 'priceId', ''),
    };
    getClientSecretKey(payload)
      .then((data) => {
        if (get(data, 'statusCode') === 409) {
          setClientSecret(get(data, 'data.clientSecret', ''));
          setSubscriptionId(get(data, 'data.id', ''));
        } else if (get(data, 'statusCode') === 200) {
          setClientSecret(get(data, 'data.clientSecret', ''));
          setSubscriptionId(get(data, 'data.id', ''));
        }
      })
      .catch((e) => {
        console.error('Error fetching client secret:', e);
      });
  }, [getClientSecretKey, planDetail, selectedPlanDetalils]);

  const fonts = [
    {
      family: 'MaisonNeueMono',
      src: 'url(https://smash-fonts.s3.eu-west-2.amazonaws.com/maisonNeue/MaisonNeue-Mono.woff)',
      weight: '400',
    },
  ];

  const appearance = {
    theme: 'stripe',
    fontFamily: 'MaisonNeueMono',
    rules: {
      '.Input': {
        borderRadius: '0px',
        fontFamily: 'MaisonNeueMono',
        display: 'flex',
        padding: '12px 8px',
        alignItems: 'center',
        gap: '10px',
        alignSelf: 'stretch',
        border: '2px solid var(--navy, #05012D)',
        background: 'var(--light, #FFF)',
      },
      '.Input:focus': {
        border: '1px solid #A1A6AD', // Default focus border color
        boxShadow: 'none', // Remove box shadow on focus
      },
      '.Label': {
        color: 'var(--navy, #05012D)',
        fontFamily: 'MaisonNeueMono',
        fontSize: '14px',
        fontStyle: 'normal',
        fontWeight: '400',
        lineHeight: '150%' /* 21px */,
      },
      '.TermsText': {
        // display: 'none',
        fontFamily: 'MaisonNeueMono',
        fontSize: '0px',
      },
    },
    variables: {
      colorPrimary: '#0570de',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'MaisonNeueMono',
    },
  };

  return !clientSecret ? (
    <Loader
      customStyle={{ margin: '-18px 0 0 -48px' }}
      isVisible={!clientSecret}
    />
  ) : (
    <>
      <div
        className="p-4 p-sm-4 p-md-5 p-lg-5 bg-white d-block d-sm-block d-md-none d-lg-none"
        style={{
          borderBottom: `1px solid #05012D`,
        }}
      >
        <PlanDetails
          priceText={priceText}
          promoCodeDetails={promoCodeDetails}
          selectedPlanDetalils={selectedPlanDetalils}
        />
      </div>
      <Elements
        stripe={stripePromise}
        className={Styles.stripePaymentElement}
        options={{ clientSecret, appearance, fonts }}
      >
        <PaymentScreen
          clientSecret={clientSecret}
          subscriptionId={subscriptionId}
          campaignDetails={campaignDetails}
          planDetail={planDetail}
          setShowPaymentScreen={setShowPaymentScreen}
          getPaymentIntent={getPaymentIntent}
          getManageSubscriptionLink={getManageSubscriptionLink}
          applyCouponCode={applyCouponCode}
          setClientSecret={setClientSecret}
          setPriceText={setPriceText}
          removeCouponCode={removeCouponCode}
          selectedPlanDetalils={selectedPlanDetalils}
          setPromoCodeDetails={setPromoCodeDetails}
        />
      </Elements>
    </>
  );
};

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  token: state.auth.token,
});

const mapDispatchToProps = (dispatch) => ({
  getClientSecretKey: (payload) => dispatch(getClientSecretKey(payload)),
  getPaymentIntent: (payload) => dispatch(getPaymentIntent(payload)),
  getManageSubscriptionLink: (payload) =>
    dispatch(getManageSubscriptionLink(payload)),
  applyCouponCode: (payload) => dispatch(applyCouponCode(payload)),
  removeCouponCode: (payload) => dispatch(removeCouponCode(payload)),
  setBillingCycle: (payload) => dispatch(setBillingCycle(payload)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(PaymentWrapper));
