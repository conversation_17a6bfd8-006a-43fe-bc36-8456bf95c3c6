import React from 'react';
import styles from './badge.module.scss';
import classNames from 'classnames';

/**
 * Badge Component
 * A reusable badge for various purposes like status, category, tag, etc.
 */
const Badge = ({ list = [], customClass = '' }) => {
  return (
    <div className={classNames(styles.badgeContainer, customClass)}>
      {list.map((badge, index) => (
        <div
          key={index}
          className={classNames(styles.badge, badge.className)}
          role="status"
          aria-label={badge.label}
          style={badge.style}
        >
          <p className="m-0 p2">{badge.label}</p>
        </div>
      ))}
    </div>
  );
};

export default Badge;
