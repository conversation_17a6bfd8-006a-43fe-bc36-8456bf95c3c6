/* eslint-disable */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import { withTranslation } from 'react-i18next';
import { get } from 'lodash';
import Style from '../styles/gettyAndUppy.module.scss';
import style from '../styles/gettyAndUppy.module.scss';
import Icon from 'sharedComponents/Icon/Icon';
import UploadButtonSvgPath from 'svgpath/UploadButtonSvgPath';
import CrossIconSvgPath from 'svgpath/CrossIconSvgPath';
import FileUpload from 'sharedComponents/FileUploder/FileUpload';
import GettyImages from './gettyImages';
import { createGettySignIn } from 'reducer/project';

class index extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      imageUrlList: [],
    };
    this.fileUploadRef = React.createRef();
  }

  componentDidMount() {
    // Style the file input to be invisible
    if (this.fileUploadRef.current) {
      const fileInput = this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.style.opacity = '0';
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.top = '-9999px';
        fileInput.style.width = '1px';
        fileInput.style.height = '1px';
      }
    }
  }

  setUppyModal = () => {
    // Trigger the file input directly
    if (this.fileUploadRef.current) {
      const fileInput = this.fileUploadRef.current.querySelector('input[type="file"]');
      if (fileInput) {
        fileInput.click();
      }
    }
  };

  addRemoveImage = (item, action) => {
    const uri = get(item, 'display_sizes[0].uri');
    if (action === 'remove') {
      this.setState((prevState) => ({
        imageUrlList: prevState.imageUrlList.filter((i) => i.uploadURL !== uri),
      }));
    } else {
      this.setState((prevState) => ({
        imageUrlList: [
          ...prevState.imageUrlList,
          {
            uploadURL: uri,
            source: 'getty',
            meta: { name: 'Getty', caption: 'Getty Image' },
          },
        ],
      }));
      this.props.uploadImageHandler(uri);
    }
  };

  render() {
    const {
      t,
      className,
      isModalShow,
      isGettyAuthorized,
      modalSize,
      uppyId,
      projectPreviewData,
      setModal,
      userData,
      createGettySignIn,
      gettyStatus,
      uploadImageHandler,
    } = this.props;
    const { imageUrlList } = this.state;
    const theme = get(projectPreviewData, 'theme');
    return (
      <>
        <div
          className={`${
            theme === 'dark' || theme === 'light' || theme === ''
              ? Style.gettyHeader
              : 'bg-primary'
          }`}
          style={
            theme === 'bcsTemplate' ||
            theme === 'bcsTemplate2' ||
            theme === 'bcsTemplate3'
              ? { width: '92%', margin: '0 auto' }
              : {}
          }
        >
          <section
            className={`${
              theme === 'dark' || theme === 'light' || theme === ''
                ? style.mainContainer
                : 'bg-primary'
            } d-flex`}
          >
            {theme === 'dark' || theme === 'light' || theme === '' ? (
              <div className={`${style.uploadImage} w-100`}>
                <div className="upload-btn-wrapper-img">
                  <div
                    className="imgUpload --circle  --color"
                    onClick={() => this.setUppyModal()}
                    data-cy="artworkEditModal"
                  >
                    <Icon
                      iconSize="48px"
                      color={
                        get(projectPreviewData, 'theme') === 'light'
                          ? '#414141'
                          : '#e4e4e4'
                      }
                      icon={UploadButtonSvgPath}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="col-12 p-0 d-flex flex-row">
                <div className="col-6 bg-white">
                  <div className="col-12 p-5">
                    <p className="text-center lead">Upload files</p>
                    <div
                      style={{
                        borderStyle: 'dashed',
                        width: '100%',
                        height: '150px',
                        borderRadius: '10px',
                        backgroundColor: '#FFFFFF',
                      }}
                      className="d-flex align-items-center justify-content-center"
                      onClick={() => this.setUppyModal()}
                    >
                      <p className="text-center">browse files here</p>
                    </div>
                  </div>
                </div>
                <div className="col-6">
                  <div className="col-12 pt-5 pb-5 pl-2 pr-2">
                    <div className="col-12">
                      <p className="text-center lead">
                        Use free Getty Images content
                      </p>
                    </div>
                    <div className="col-12 d-flex justify-content-center">
                      <GettyImages
                        t={t}
                        isGettyAuthorized={isGettyAuthorized}
                        projectPreviewData={projectPreviewData}
                        addRemoveImage={this.addRemoveImage}
                        className={className}
                        show={isModalShow}
                        size={modalSize}
                        setModal={setModal}
                        userData={userData}
                        createGettySignIn={createGettySignIn}
                        gettyStatus={gettyStatus}
                        uploadImageHandler={uploadImageHandler}
                        imageUrlList={imageUrlList}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
            {(theme === 'dark' || theme === 'light' || theme === '') && (
              <div className={Style.partition} />
            )}
          </section>
        </div>

        {/* FileUpload component visible but file input styled to be invisible */}
        <div 
          ref={this.fileUploadRef}
          data-cy="uploadImageArtwork"
          style={{ 
            position: 'relative',
            width: '1px',
            height: '1px',
            overflow: 'hidden'
          }} // Keep component in DOM but minimal size
        >
          <FileUpload
            name="projectArtwork"
            accept={['jpeg', 'jpg', 'png']}
            onSuccessHandler={(result) => {
              const url = result.projectArtwork?.url || result.projectArtwork;
              uploadImageHandler(url);
            }}
            enableCropping={true}
            cropShape="rect"
            cropHeight={320}
            className="uploadStyleBtn"
          />
        </div>

        {(theme === 'dark' || theme === 'light' || theme === '') && (
          <div className="w-100 mt-5 pt-4">
            <GettyImages
              t={t}
              isGettyAuthorized={isGettyAuthorized}
              projectPreviewData={projectPreviewData}
              addRemoveImage={this.addRemoveImage}
              className={className}
              show={isModalShow}
              size={modalSize}
              setModal={setModal}
              userData={userData}
              createGettySignIn={createGettySignIn}
              gettyStatus={gettyStatus}
              uploadImageHandler={uploadImageHandler}
              imageUrlList={imageUrlList}
            />
          </div>
        )}
      </>
    );
  }
}
const mapStateToProps = (state) => {
  return {
    gettyStatus: state.project.gettyStatus,
  };
};
const mapDispatchToProps = (dispatch) => {
  return {
    createGettySignIn: (data) => dispatch(createGettySignIn(data)),
  };
};

index.defaultProps = {
  className: '',
  modalSize: 'lg',
  uppyId: 'uppyId',
};

index.propTypes = {
  uppyId: PropTypes.string,
  modalSize: PropTypes.string,
  className: PropTypes.string,
  t: PropTypes.func.isRequired,
  isGettyAuthorized: PropTypes.bool.isRequired,
  isModalShow: PropTypes.bool.isRequired,
  closeHandler: PropTypes.func.isRequired,
  uploadImageHandler: PropTypes.func.isRequired,
  projectPreviewData: PropTypes.object.isRequired,
  userData: PropTypes.object.isRequired,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withTranslation('common')(index)));
