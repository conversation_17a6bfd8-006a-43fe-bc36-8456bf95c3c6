import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { pushToDataLayer } from 'lib/commonUtil';
import { forEach, get } from 'lodash';
// import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Modal from '../../../Modal/modal';
import ShowImage from './showImages';
import EditImage from '../../../CommonEditMode/Moodboard/moodboard';

class moodboardTemplate extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      descModalStatus: false,
    };
  }

  componentDidMount() {
    const { projectPreviewData, setImageData } = this.props;
    const artWork = get(projectPreviewData, 'artWork', []);
    // method to set image data coming from api
    setImageData(artWork);
  }

  // method to open edit section of image artwork
  editFormToggleHandler = () => {
    const { setSaveButtonStateArtWork } = this.props;
    setSaveButtonStateArtWork(true);
    pushToDataLayer({
      action: 'getty click edit',
      category: 'Project moodboard',
      label: 'Click edit moodboard section',
    });
  };

  // method to close edit section of image artwork
  closeEdit = () => {
    const { setSaveButtonStateArtWork } = this.props;
    setSaveButtonStateArtWork(false);
    this.removeHashFromUrl();
  };

  // method to save the data of uploaded images
  saveData = () => {
    const { updateProjectArtwork } = this.props;
    updateProjectArtwork('close', null, null, null);
    this.removeHashFromUrl();
  };

  // method to remove the data from url
  removeHashFromUrl = () => {
    const currentUrl = window.location.href;
    const url = currentUrl.split('#');
    window.history.pushState(currentUrl, 'Overview', url[0]);
  };

  // method to send all the uploaded image data to api
  setImage = (data) => {
    const {
      setImageData,
      updateProjectArtwork,
      projectPreviewData,
      imageList,
    } = this.props;
    const projectId = get(projectPreviewData, '_id');
    
    // Handle Getty image data
    if (get(data, 'id')) {
      const imagesArray = [];
      const images = {};
      images.url = data.display_sizes[0].uri;
      images.title = data.title;
      images.disc = data.title;
      images.source = 'getty';
      imagesArray.push(images);

      forEach(imageList, (item) => {
        imagesArray.push(item);
      });

      setImageData(imagesArray);
      updateProjectArtwork(
        'save',
        imagesArray,
        projectId,
        'artWorkImage',
        'Image added',
      );
    } 
    // Handle array of image objects (from existing functionality)
    else if (Array.isArray(data)) {
      const images = data.map((item) => {
        return {
          url: item.uploadURL,
          title: item.meta.name,
          disc: item.meta.caption,
          source: item.source,
          id: item._id,
        };
      });

      forEach(imageList, (item) => {
        images.push(item);
      });

      setImageData(images);
      updateProjectArtwork(
        'save',
        images,
        projectId,
        'artWorkImage',
        'Image added',
      );
    }
    // Handle single URL string (from FileUpload)
    else if (typeof data === 'string') {
      const imagesArray = [];
      const newImage = {
        url: data,
        title: 'Uploaded Image',
        disc: 'Uploaded Image',
        source: 'fileupload',
        _id: Date.now().toString(), // Generate temporary ID
      };
      imagesArray.push(newImage);

      forEach(imageList, (item) => {
        imagesArray.push(item);
      });

      setImageData(imagesArray);
      updateProjectArtwork(
        'save',
        imagesArray,
        projectId,
        'artWorkImage',
        'Image added',
      );
    }
  };

  // Called for set modal status true.
  isOpenInfoModal = () => {
    this.setState({
      descModalStatus: true,
    });
  };

  // Called set modal status false.
  closeDescModal = () => {
    this.setState({
      descModalStatus: false,
    });
  };

  // this method is used for modal body
  body = () => {
    const { t } = this.props;
    return (
      <div>
        <div className="text-left">
          <p className="regularRoboto">
            {t('common:projects.overview.basicInfo.artwork')}
          </p>
        </div>
      </div>
    );
  };

  render() {
    const {
      t,
      isDisable,
      setSaveButtonStateArtWork,
      showButtonArtwork,
      removeSectionItems,
      projectPreviewData,
      imageList,
      setImageData,
      updateProjectArtwork,
      updateCoverInfo,
      userData,
      onOffArtworkStatus,
      setMoodBoardImage,
    } = this.props;
    const { descModalStatus } = this.state;
    return (
      <div
        className={`${
          onOffArtworkStatus === 'lock' && 'sectionHide'
        } col-12 p-0`}
      >
        <Modal
          modalShow={descModalStatus}
          title="Mood Board"
          body={this.body()}
          closeCallback={() => this.closeDescModal()}
          isShowCrossBtn
          divider
        />
        <div className="d-none">
          <div onClick={this.editFormToggleHandler} id="artWorkPencilBtn" />
        </div>

        {!showButtonArtwork ? (
          <ShowImage
            isDisable={isDisable}
            setSaveButtonStateArtWork={setSaveButtonStateArtWork}
            showButtonArtwork={showButtonArtwork}
            imageList={imageList}
            setImageData={setImageData}
            isOpenInfoModal={this.isOpenInfoModal}
            setMoodBoardImage={setMoodBoardImage}
          />
        ) : (
          <EditImage
            t={t}
            imageList={imageList}
            setImage={this.setImage}
            removeSectionItems={removeSectionItems}
            projectPreviewData={projectPreviewData}
            setImageData={setImageData}
            updateProjectArtwork={updateProjectArtwork}
            updateCoverInfo={updateCoverInfo}
            userData={userData}
            isDisable={isDisable}
            saveData={this.saveData}
            closeEdit={this.closeEdit}
            isOpenInfoModal={this.isOpenInfoModal}
          />
        )}
      </div>
    );
  }
}

moodboardTemplate.propTypes = {
  t: PropTypes.func.isRequired,
  isDisable: PropTypes.bool.isRequired,
  showButtonArtwork: PropTypes.bool.isRequired,
  setSaveButtonStateArtWork: PropTypes.func.isRequired,
  projectPreviewData: PropTypes.func.isRequired,
  updateProjectArtwork: PropTypes.func.isRequired,
  router: PropTypes.func.isRequired,
  imageList: PropTypes.func.isRequired,
  setImageData: PropTypes.func.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
  updateCoverInfo: PropTypes.func.isRequired,
  snapStatus: PropTypes.bool.isRequired,
  userData: PropTypes.func.isRequired,
  iconHide: PropTypes.bool.isRequired,
  onOffArtworkStatus: PropTypes.string.isRequired,
};

export default moodboardTemplate;
