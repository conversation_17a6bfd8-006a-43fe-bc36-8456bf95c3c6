@import '../../styles/variable.scss';

.layoutRoot {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: $light-grey;
}

.layoutContainer {
  display: flex;
  flex: 1;
  position: relative;
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 100;
  transition: width 0.3s ease;
  background-color: $white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.sidebarOpen {
  width: 250px;
}

.sidebarClosed {
  width: 74px;
}

.bannerContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 40px;
  z-index: 1010;
  box-sizing: border-box;
  display: block;
  pointer-events: auto;
}

.mainContent {
  flex: 1;
  transition:
    margin-left 0.3s ease,
    padding-top 0.3s ease;
  min-height: 100vh;
  padding: 20px;
  background-color: $light-grey;
}

.contentWithSidebar {
  margin-left: 250px;
}

.contentWithCollapsedSidebar {
  margin-left: 74px;
}

/* Banner is now part of the normal page flow */

.mobileHeader {
  position: sticky;
  top: 0;
  z-index: 99;
  width: 100%;
  background-color: $white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pageTitle {
  margin-bottom: 24px;

  h1 {
    font-size: 24px;
    font-weight: 600;
    color: $navy;
    margin: 0;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 250px;
  }

  .sidebarOpen {
    transform: translateX(0);
  }

  .mainContent {
    margin-left: 0 !important;
    padding: 16px;
  }

  .layoutContainer {
    margin-top: 60px; /* Space for mobile header */
  }
}
