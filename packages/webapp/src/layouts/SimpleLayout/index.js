import React from 'react';
import PropTypes from 'prop-types';
import Header from '../../sharedComponents/Header/header';
import styles from './SimpleLayout.module.scss';

/**
 * SimpleLayout - Layout component without sidebar for authentication and simple pages
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render in the layout
 * @param {boolean} props.showHeader - Whether to show the header
 * @param {boolean} props.fullWidth - Whether to use full width layout
 * @param {string} props.backgroundColor - Background color for the layout
 * @returns {JSX.Element} - Rendered component
 */
const SimpleLayout = ({ 
  children, 
  showHeader = true, 
  fullWidth = false,
  backgroundColor = '#F7F7F3'
}) => {
  return (
    <div 
      className={styles.layoutRoot}
      style={{ backgroundColor }}
    >
      {showHeader && <Header />}
      
      <div className={`${styles.layoutContainer} ${fullWidth ? styles.fullWidth : ''}`}>
        <div className={styles.content}>
          {children}
        </div>
      </div>
    </div>
  );
};

SimpleLayout.propTypes = {
  children: PropTypes.node.isRequired,
  showHeader: PropTypes.bool,
  fullWidth: PropTypes.bool,
  backgroundColor: PropTypes.string
};

export default SimpleLayout;
