import React from 'react';
import { LayoutProvider } from '../contexts/LayoutContext';
import MainLayout from './MainLayout';
import SimpleLayout from './SimpleLayout';

/**
 * Higher-order component to wrap pages with the appropriate layout
 * 
 * @param {React.ComponentType} Component - The page component to wrap
 * @param {Object} options - Layout options
 * @param {string} options.layout - Layout type ('main' or 'simple')
 * @param {Object} options.layoutProps - Props to pass to the layout component
 * @returns {React.ComponentType} - Wrapped component with layout
 */
const withLayout = (Component, options = {}) => {
  const {
    layout = 'main',
    layoutProps = {}
  } = options;

  const WrappedComponent = (props) => {
    // Get profile from props or use empty object
    const profile = props.userData?.profile || {};

    // Choose the appropriate layout based on the layout option
    const LayoutComponent = layout === 'simple' ? SimpleLayout : MainLayout;

    // For MainLayout, pass the profile
    const layoutComponentProps = layout === 'main' 
      ? { profile, ...layoutProps } 
      : layoutProps;

    return (
      <LayoutProvider>
        <LayoutComponent {...layoutComponentProps}>
          <Component {...props} />
        </LayoutComponent>
      </LayoutProvider>
    );
  };

  // Set display name for debugging
  WrappedComponent.displayName = `withLayout(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
};

export default withLayout;
