aws_region  = "eu-west-2"
aws_profile = "092595024884"
env         = "stage"

public_fqdn = "api.stage.mysmash.media"

service_count              = "1"
service_name               = "api"
service_port               = "3000"
service_memory_reservation = "64"
service_memory             = "256"
service_cpu                = "128"
ulimit_nofile              = "1024"

# ENV vars
env_vars = {
  ALLOWED_ORIGIN = {
    type  = "String"
    value = "https://app.stage.mysmash.media,https://ops.stage.mysmash.media"
  }

  APP_BASE_URL = {
    type  = "String"
    value = "api.stage.mysmash.media"
  }

  APP_HOST = {
    type  = "String"
    value = "0.0.0.0"
  }

  APP_PORT = {
    type  = "String"
    value = "3000"
  }

  APP_PROTOTYPE = {
    type  = "String"
    value = "https"
  }

  APP_VERSION = {
    type  = "String"
    value = "1.0.0"
  }

  APP_ENV = {
    type  = "String"
    value = "staging"
  }

  AUTH0_DOMAIN = {
    type  = "String"
    value = "mysmash.eu.auth0.com"
  }

  AUTH_CHECK = {
    type  = "String"
    value = "https://roles/role"
  }

  AWS_BUCKET_NAME = {
    type  = "String"
    value = "im-files-stage-20240118101422679700000001"
  }

  AWS_REGION = {
    type  = "String"
    value = "eu-west-2"
  }

  CACHE_EXPIRY = {
    type  = "String"
    value = "129600000"
  }

  CACHE_MAX_STORE = {
    type  = "String"
    value = "500"
  }

  DB_URL = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AFsShbHy5TePoBHy0HbUOS6AAAArTCBqgYJKoZIhvcNAQcGoIGcMIGZAgEAMIGTBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDEi9ElB4lM94e7fIoAIBEIBmsn8WidBWtbvODm3DN4CU9VLiHCcQhRnwAW0fSLNUA4KN0KY9b4ltSFXoQw45rFJpXEgNe1vp+WVhLyJwfLPxSO2KYhzIcl6Urv+aSf2EdqbuOIEWMKadLQ0iZxRKRqXr/nQush/n"
  }

  DEBUG_LOG = {
    type  = "String"
    value = "true"
  }

  DEFAULT_API_VERSION = {
    type  = "String"
    value = "1"
  }

  IS_ELASTIC_DISABLE = {
    type  = "String"
    value = "true"
  }

  ELASTIC_API_ID = {
    type  = "String"
    value = "1Fk0MHQBZ24pb6YyQBLY"
  }

  ELASTIC_API_KEY = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGMyJkckcNK1j8zfb+BHmt/AAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMLgEQwSKey21jvtqgAgEQgDF8xBTSq0C++Tqi9rhcnhHW+Xx+SBiXoDdxpIRqQrMJDW3boJKc81PwJ1SoSbjavRX8"
  }

  ELASTIC_APM_SECRET_TOKEN = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEckuo9+czkz2ayjCNwiCCSAAAAbTBrBgkqhkiG9w0BBwagXjBcAgEAMFcGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMN8og2FPsBo4p3butAgEQgCpiTfcOi7q820kXMXuE74p3SvO4ETvqwYdUNEez5Qso5T0NA0oAs3HmOeA="
  }

  ELASTIC_APM_SERVER_URL = {
    type  = "String"
    value = "to_be_set_later"
  }

  ELASTIC_APM_SERVICE_NAME = {
    type  = "String"
    value = "SMASH_API_ON_stage"
  }

  ELASTIC_SEARCH_INDEX = {
    type  = "String"
    value = "smash_stage"
  }

  ELASTIC_SEARCH_URL = {
    type  = "String"
    value = "https://1f70e1cf5c3a4ee98ee7abae9e9d8926.eu-west-1.aws.found.io:9243"
  }

  IDENTITY_MANGER_URL = {
    type  = "String"
    value = "https://api.im.stage.mysmash.media"
  }

  IGNORE_URLS_LOG = {
    type  = "String"
    value = "/v1/health"
  }

  IMG_COMPRESS_LEVEL = {
    type  = "String"
    value = "9"
  }

  IMG_QUALITY = {
    type  = "String"
    value = "60"
  }

  INIT_RECORD = {
    type  = "String"
    value = "10"
  }

  IS_APM = {
    type  = "String"
    value = "false"
  }

  LOG_PAYLOAD = {
    type  = "String"
    value = "false"
  }

  LOG_REQUEST_COMPLETE = {
    type  = "String"
    value = "false"
  }

  LOG_REQUEST_START = {
    type  = "String"
    value = "false"
  }

  M2M_API_URL = {
    type  = "String"
    value = "https://mysmash.eu.auth0.com/oauth/token"
  }

  M2M_CLIENT_ID = {
    type  = "String"
    value = "GC6QDDVYFsR0PwQEzlXpwQuMEYW5Tzpm"
  }

  M2M_CLIENT_SECRET = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AH6hn004bpqwk/3DCKmpNvwAAAAojCBnwYJKoZIhvcNAQcGoIGRMIGOAgEAMIGIBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDLivBmlZbZ1Emj4VKwIBEIBbL0dElcz1EuoduGqG4wkPcYEIq6ZUUF7bJ35CP45yurHn9lzdO8Tna3CyFk8d0PkhWTClr3sQI2mcqkpJ0LvZhADM0jBK80N1rfX0Pks1QmotaidX7STiTxscFw=="
  }

  MAX_IMAGE_SIZE_ARTWORK = {
    type  = "String"
    value = "10485760"
  }

  SKIP_USER_VALIDATION_ON_ROUTES = {
    type  = "String"
    value = "/v1/auth/createUser,/v1/auth,/v1/company/{id},/v1/company/list"
  }

  VALID_API_VERSIONS = {
    type  = "String"
    value = "1,2"
  }

  GETTY_CLIENT_ID = {
    type  = "String"
    value = "c6uy2upr67mfss9dmbnv2pbx"
  }

  GETTY_CLIENT_SECRET = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AG2TvrGCnlOoTC6oMssIC/PAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMi5f+gJhzW/PvS9RpAgEQgC8OmkE6dIqdS9awxZ83BSbJhHIyUMxn6zl9rIYS7DlCKanO1gvBeHDwdbO/4avPlQ=="
  }

  GETTY_AUTH_URL = {
    type  = "String"
    value = "https://api.gettyimages.com/oauth2/token"
  }

  GETTY_AUTH_REDIRECT_URI = {
    type  = "String"
    value = "https://app.stage.mysmash.media/project/getty/verify"
  }

  AGENDA_DB_URL = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGDLAtUnvTfV9KSUj9fZZCTAAAArDCBqQYJKoZIhvcNAQcGoIGbMIGYAgEAMIGSBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDARQFC4qo524d5cfwgIBEIBlYegoSDMARM0Z6Pz8RCh49+53gzIB9wyg/KgnKYRC6JF0c7/48g/EJXROla2KVoOs4ce2LefZI98JI82pz7SVlCm/sY7DHAhfjOZm215C+U7LbkeNYweTn3QTze00hMuntK1uSTs="
  }

  WEBAPP_BASE_URL = {
    type  = "String"
    value = "https://app.stage.mysmash.media"
  }

  OPS_BASE_URL = {
    type  = "String"
    value = "https://ops.stage.mysmash.media"
  }

  MANDRILL_API_KEY = {
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEcfb2ikixSL9TdVLNoi9gmAAAAdDByBgkqhkiG9w0BBwagZTBjAgEAMF4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMCuH7zVEHHKIjR8EyAgEQgDEZguCUEaOtCA1m0hL4J7VZCqWeSyqq5zFL6Rv5hNnCUfJIaL9jFqueMLjHcvKVaOYh"
  }

  GETTY_SPREADSHEET_CLIENT_EMAIL={
    type  = "String"
    value = "<EMAIL>"
  }

  GETTY_SPREADSHEET_PRIVATE_KEY={
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGAlHf9sIdjTAlanN0CUNayAAAHMDCCBywGCSqGSIb3DQEHBqCCBx0wggcZAgEAMIIHEgYJKoZIhvcNAQcBMB4GCWCGSAFlAwQBLjARBAxWHEh50KKrpn2IRskCARCAggbjGXUErxvr2CGFkrDqPRU29jlllMSpt4Zz4KLTEw74MjKyWKxH8VNc34mnBLEArlL8CKCqg4Zwq+hWzPa7EOpHIWZcVsnrq/NtWHblDw0kZg0Bok48/Zh+xRUTToRRS4LinePTslesBvoTW1wPYMn+1l0J8v3UrcyXKUML/txdw7r8nfN4aBuKxU2Gz1jcPrQxuqddqvTcqvQo2Owh9mmTsAgNvzQ49R25ZEHHH1aF2kcVp8QMS7XHUFqOdOxEWosQwR0OnWHlO+MLGqwttRDLBR3kGiJMTk0vLzjACP2wNWyvYYSgxxgF1zTy5yziGS6kxCP+kFByGA5MDVUz+0/lhwD63wXorR5phEkEKyMVAq+2zauKqM1HPEnmV/RsUv7GySIfu2px8Len1s0KNNcfOF0qdxuePLkjjIOR4wrJVWZCIF+88nTegaMNoGdB7oJrYBUFFTNiLH4Z7imlQKSbHN0WprbbfIfsMNVzBWCF7FBW1Onfj9Ql2JJqHDON7AmCPuckw1aBvy180HvRjo+HYKVhe94ZIIUetIZXEwSWqSL8IOrKGY9KuoxrunhBGIq3BrJwJgaXI4NU0WU/HyGUQumXmMJAJHFSXZkk6ezMji5Gq/03SAJ/Px9fXO8ahYw2EvJQhvqtITR8jZSD29PlBIeyvRo4cr9K7E3yIwQWvPA0Qu+9eUE2gbzKBjxszZZCGnt3XqoUSxCQ+M40I03oStZwlo0m/bnDnxd+iwRgxWWHmTkGNKiZNtF51nKoZBzU7YZaWuaPEvClEOMjMDdxLzk7tFPPH6K5pC1Aa1NU8/6K+KlzKr4UfGYyCfwJ7Tb02k+73nWo1iVlwiQIRw/648W144r7ISbLZ3v2P+rjYzfeZjY0tdmAClW+iE7uAA/+DLXcwkoQZDR04Vhla1nZAP/a0Q0kzaQdVp5ZOn25dc6gooBdeZ2ThOfdqliDVK1cfLF3cropXmHU0pCsL1pbQurzwIf8jIt0kIqExOT42F+Ud6E/2Enwj6fljsFWYHBSPH685JPXiE0GXIgPd/942/Jg0d5r3RsdwxuZtp8yv7zK2+YAvhARWVt42IZ2+3YbBaD8md5wJXm8gQtGLGkG3bDpOW851nTLWcWpjl5qa/JLQpVcIRWL5IFbcBlyBYEW6ArB285QfCJl44LUruEyt89bmdDuU4TzrKonwCyTzak1DFbm1gi539YhHOeCoXaeuDz/cN/eExQfDcYx4vS5PRc626mONuSBrf5MkW1CWFDjO8WIOk+nyCwqTmDvZIzEz27B0zdMmj19YWLjS00dHeboWv/MzmhhZ6VLu+ny/CwbJZjUioNmidZlAryjgPK7ZyNGvlcV/u+5j547q082jM+AkCCC2e3ESWCP2HkhwuXhaLUnFrlDkUkOMUfgD+D815/y3Q4zeISS6t8vPpooeDo0ZVvxrrarQ9DSAvB2RZ1pFH/Cvk2msI8KdkXsTuYmGAdXW4KScd8AOGnvJir1zqmuwg8ePocWqdXp80GiarcfFlc0AknLjtrk1hSotWcDQpierMxp9aKwUzuXE0iYsiD7SNqjf1p5U3ZC2PjvDOuLiipPvRei0L6zkt41zcITAtKiOLWNpD79C+IdMLYGzVPAIt90zH4D2ibxfhawmgZ4m615x+uh0sfmfHFTb3WrqhqahuxbeRQyTDMtMpAN64KuDK9zACSU7dl4Adz3h/BQ5ESR6aggxizMaYDAmEu2EzxwOXCvqBizz/LEp5I7R2PvcejaRa444NCqHBsyOuRgroN18j86llZozXkHDxmrWno2e3NuV0FArvppUUfrBMnd5N7ypHHtRe7V1/aNvyU2qEqOMxKY7sOPz/4HuED2+vDHa8i664ZGDvtCg23a7B0tDnFNa6GfGyMEVRkK9EDTJN6vEfW17yp9EAiubQr19pXGBcG4goZKNRRNwvh/rrNvcq+sJ37UdqWCpPtA4EDCHYepzv3wlvUi8WiDP2YI6VB/ahCcfqkIhpKQxmnhM67Vs1k5k1GZl+Zflh6JqI7xx4vo7T2PN/KbKg8u8BUU3Z/50BoXIig+6GyPZBX/CPxuDcfg4QEh0V3h2DaxSzpFteTu5t4pqTpTEd/O4dz8gCLxRSe8kyOXDwZDU/aLBYDwvQo3rRL/1fbSEqmgTxXKnx4FeOMNIW/MiLvRiGZDct7v8unt2lt1p4D0kDBOCrfPeJr+2gJXQMNT9FHXTX506krCLuzsfjJVvyM5AZSKXIeodEIR2t3sXv20h3nlQCkqDaAVBytLYbWpk7xky6KyDA4LpsNn4QLIooU2VcbS9ru2wvVIIYQWPTSncBAOYWthccoOimurgmXfDnQwmZV0rJg="
  }

  GETTY_SPREADSHEET_ID={
    type  = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AFfxC3GDuPi2JcQGx2VmeS2AAAAizCBiAYJKoZIhvcNAQcGoHsweQIBADB0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDH2FZ/9mX2oEm/IcTAIBEIBHY2b4Y+qxf3oB11KHk3Z7JeEU+9m6HlomAseo5zVL0cPYwbMSz89vlYqflqmJIfA7cZVSz5pm90EwizDUnBEojehHQmxfqk8="
  }

  GETTY_SPREADSHEET_RANG={
    type  = "String"
    value = "Sheet1"
  }

  MAILCHIMP_SERVER_PREFIX={
    type  = "String"
    value = "us17"
  }

  MAILCHIMP_MEMBERS_LIST_ID={
    type  = "String"
    value = "1bb602fb83"
  }

  MAILCHIMP_ENV={
    type  = "String"
    value = "stageelopment"
  }

  SOCKET_ALLOWED_ORIGIN = {
    type = "String"
    value = "https://app.stage.mysmash.media"
  }

  CRM_AGENDA_DB_URL = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AFB/F7G/Dk8LHkk3yVMdLXmAAAAsjCBrwYJKoZIhvcNAQcGoIGhMIGeAgEAMIGYBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDGEzqq6qhdOnw/+tfgIBEIBr7gH9gkAeJ15FYVCASc0ZVJCTqHckuR4D1P1x/BmK1ZTcN1XQOwfLARAUmCZ2HYsolOsiiAaTcGJCTsrQszAW14y1dLFfFGzaHnS1YkLa9A3oD59hBe0FqvJoHSNu743m21fdPf0NKnuOdH4="
  }

  CUSTOMER_SITE_ID = {
    type = "String"
    value = "3d0ad358bfc08be2596d"
  }

  CUSTOMER_API_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AH/go0wExnjJj5dE0bhsdRrAAAAcjBwBgkqhkiG9w0BBwagYzBhAgEAMFwGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMDGQgDSgqmVxjHfzBAgEQgC9ka4WbzEMfV+9SPE7MR3XIgIIQEsTk5iM0Tx98RzvVfZ87uFdCHzIpIpCEp//9EQ=="
  }

  CUSTOMER_GETTY_EMAIL_API_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AF/8a7T1CNtn7RxXTQ432hpAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMfk4YdEtFNIYSgLD1AgEQgDtM7w3X5Ck9WwHMx58LTNPOWA+WNP6VGc70vazi2BQ1NhIXOmc15ayAScIHJNcN+V2jQBDJM/HLuKj9eA=="
  }

  STRIPE_SECRET_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGDE5m8d/QIuJNMh1/ynuhNAAAAzjCBywYJKoZIhvcNAQcGoIG9MIG6AgEAMIG0BgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDH6VXx35oP7dpsjj5wIBEICBhoOOWG69WN5wiAja+1ZgS3dwL+gf9D7hT69dIJJNLdLblbOIgxPQ6j6uuknw/PhlweNMIrxAERqWZ5OYZRUpgPqCc1T78PUNJDt028NQlmvTcIMGIekS2bySIQmv3PVhzURNC0XuONPG1WbfZx8fPNikdbqg9ijc1Huq/0tsd0+lyfv+sT5Y"
  }

  STRIPE_WEBHOOK_SECRET = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEX67+HRJLhI742fRvGxIsmAAAAhTCBggYJKoZIhvcNAQcGoHUwcwIBADBuBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDD4P74Gh+w6twXKvewIBEIBBoYt6c337jX8cmqOg6Ns92JAQQrlP64raMCTy2894dDLXRkCkJbIFfRNoF1HUncsGea9Sm4DkV6kOvgPcJbGGSfU="
  }

  STRIPE_PROMOTION_CODE = {
    type = "String"
    value = "promo_1Pb0PLHrMi9Sfemk5sSXUNQB"
  }

  ADMIN_EMAILS = {
    type = "String"
    value = "<EMAIL>"
  }

  CUPID_EMAILS={
   type = "String"
   value = "<EMAIL>"
  }

  EMAIL_TEMPLATE_DATA = {
    type = "String"
    value = "{\"gettyInfoTemplateId\":\"2\",\"inviteTemplateId\":\"3\",\"welcomeTemplateId\":\"4\",\"salesEstimateTemplateId\":\"5\",\"deleteUserTemplateId\":\"22\",\"accountCreateLinkTemplateId\":\"14\",\"submissionFeedbackTemplateId\":\"23\",\"calloutPublishTemplateId\":\"16\",\"slateTemplateId\":\"17\",\"calloutReadyToReviewTemplateId\":\"18\",\"paymentSucceededTemplateId\":\"20\",\"submissionSubmitEmail\":\"24\",\"rejectSubmissionEmail\":\"25\",\"slateStatusChangeTemplateId\":\"27\"}"
  }

  SALES_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  NO_REPLAY_EMAIL = {
    type = "String"
    value = "<EMAIL>"
  }

  CUPID_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  ADMIN_EMAIL_ADDRESS = {
    type = "String"
    value = "<EMAIL>"
  }

  SLACK_CHANNEL = {
    type = "String"
    value = "C07ENL7TV9Q"
  }

  SLACK_TOKEN = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AGL+8Wo2Kjk9WIKc2bTe/KTAAAAlzCBlAYJKoZIhvcNAQcGoIGGMIGDAgEAMH4GCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMxTZDzx9/JEUF3HhMAgEQgFGFUooAC9K5pJUdDnnUEnnjmGk1FrtA6wMoyu2nqNkltH5ulo/eAI8YZMEY4l07NkdnijMdJJKKWzGE6OYc4IEt0rOLobUWrfPL+IxOwBr3v0w="
  }

  PUBLIC_API_AUTH_KEY = {
    type = "SecureString"
    value = "AQICAHiboL09oVyDmfY/NdusL0meAEoT6f5m4qeDGUJCltfD9AEGx5GW5AVNVJm+tB0nOVO6AAAAdjB0BgkqhkiG9w0BBwagZzBlAgEAMGAGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMDnTXyNmjwLVW9FY8AgEQgDNskO/U4m4fhETtcUNkGpOST58+SUxkmtM8MkBS7vumL6o36aO1xJjVizFltCswqfV0ooQ="
  }

  STRIPE_TRIAL_DAYS = {
    type = "String"
    value = "1"
  }

  X_USER_EMAIL = {
    type = "String"
    value = "<EMAIL>"
  }
}
